{"informations_banque": {"nom_banque": "Banque <PERSON>Kowei<PERSON>", "forme_juridique": "S.A", "capital_social": "100 000 000", "numero_rc": "B152601006", "numero_tva": "12357", "adresse_siege": "10 bis, A<PERSON>. <PERSON> V. 8.P.40- 1001 Tunis . Tel. : (+216) 71 204 000 - Fax : (#216) 71 343 106 (+216) 71 343 106: AB - (4", "telephone": "", "fax": "71343 106", "email": "", "site_web": "www.btknet.com", "code_banque": "", "identifiant_unique": ""}, "informations_contrat": {"type_contrat": "compte de depot et aux conditi", "numero_contrat": "abrication", "date_edition": "20/06/2025", "date_signature": "", "date_echeance": "", "montant_principal": "100 000 000", "devise": "d", "duree": "", "taux_interet": "", "taux_effectif_global": "", "commission_gestion": "", "commission_engagement": "", "frais_dossier": "", "garanties": "ment minimal de trois mois", "modalites_remboursement": "", "periodicite": "", "objet_financement": ""}, "informations_client": {"code_client": "Nom", "nom": "Piece D'Tdentite Numero Adresse Client Ville Code P", "prenom": "", "nom_complet": "", "date_naissance": "20/06/2025", "lieu_naissance": "", "nationalite": "", "situation_familiale": "", "nombre_enfants": "", "profession": "", "secteur_activite": "", "employeur": "", "revenus_mensuels": "", "adresse_domicile": "Client Ville Code Postal Pays Mobile Telephone I Detail du Package PACK 000022 - <PERSON><PERSON><PERSON> KYASSI SILVER PR0DUIT 201 C0MPTE C0MPTE EPARGNE SPECIAL PR0DUIT", "ville": "", "code_postal": "", "pays": "", "telephone_fixe": "", "telephone_mobile": "00 000 000", "email": "", "cin": "15260100", "passeport": "", "permis_conduire": "", "numero_compte": "", "agence": "Informations du Client Code Client Nom Client Piec"}, "champs_masques": [], "scores_qualite": {"score_global": 38.9, "score_banque": 66.7, "score_contrat": 35.3, "score_client": 28.0, "champs_remplis_total": 21, "champs_possibles_total": 54, "champs_banque_remplis": 8, "champs_contrat_remplis": 6, "champs_client_remplis": 7, "champs_masques_detectes": 0, "taux_completion": "21/54"}, "metadata": {"fichier_source": "PACK_EXT204_250620_100511.pdf", "extraction_timestamp": "2025-08-12T17:31:34.299680", "methode_extraction": "Ultra-Intelligent Contract Extractor v3.0", "ocr_text_length": 5020, "pages_traitees": 2, "version": "3.0", "ameliorations": ["Patterns ultra-flexibles pour numéros de contrat", "Détection améliorée des CIN tunisiens", "Reconnaissance contextuelle intelligente", "Validation adaptative des champs", "Post-traitement et nettoyage automatique", "Gestion des formats masqués (X)", "Détection multi-format pour montants et dates"]}}