
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BTK Contract Analyzer Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        :root {
            --primary-blue: #003087;
            --secondary-blue: #0047AB;
            --accent-gold: #FFC107;
            --accent-gold-hover: #FFB300;
            --text-light: #ffffff;
            --text-dark: #1a1a1a;
            --bg-light: #f8fafc;
            --border-light: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: var(--text-light);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .sidebar {
            background: linear-gradient(180deg, var(--secondary-blue) 0%, var(--primary-blue) 100%);
            height: 100vh;
            position: fixed;
            width: 280px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
        }

        .content {
            margin-left: 280px;
            padding: 2rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 100vh;
        }

        .logo-section {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .logo-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-gold);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .logo-subtitle {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 400;
        }

        .nav-menu {
            padding: 1.5rem 0;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 0.75rem;
            transition: all 0.2s ease;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link:hover,
        .nav-link.active {
            color: var(--text-light);
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(4px);
        }

        .nav-link.active {
            background: var(--accent-gold);
            color: var(--primary-blue);
            font-weight: 600;
        }

        .header-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            color: var(--text-dark);
            padding: 2rem;
            border-radius: 1.5rem;
            box-shadow: var(--shadow-xl);
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-title {
            font-size: 2.25rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-subtitle {
            color: #64748b;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .upload-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 2.5rem;
            border-radius: 1.5rem;
            box-shadow: var(--shadow-xl);
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .upload-zone {
            border: 2px dashed #cbd5e1;
            border-radius: 1rem;
            padding: 3rem 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: var(--bg-light);
            position: relative;
            overflow: hidden;
        }

        .upload-zone::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, var(--accent-gold), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
            animation: rotate 3s linear infinite;
        }

        .upload-zone:hover::before {
            opacity: 0.1;
        }

        .upload-zone.dragover {
            border-color: var(--accent-gold);
            background: rgba(255, 193, 7, 0.05);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--secondary-blue);
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .upload-zone:hover .upload-icon {
            transform: scale(1.1);
            color: var(--accent-gold);
        }

        .upload-text {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .upload-subtext {
            color: #64748b;
            font-size: 0.875rem;
        }

        .file-input {
            position: absolute;
            inset: 0;
            opacity: 0;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--accent-gold), var(--accent-gold-hover));
            color: var(--primary-blue);
            padding: 1rem 2rem;
            border: none;
            border-radius: 0.75rem;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-md);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1.5rem;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            background: linear-gradient(135deg, var(--accent-gold-hover), #FF8F00);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .history-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 2rem;
            border-radius: 1.5rem;
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .history-card {
            background: #ffffff;
            color: var(--text-dark);
            padding: 1.5rem;
            border-radius: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            border: 1px solid var(--border-light);
            position: relative;
            overflow: hidden;
        }

        .history-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, var(--accent-gold), var(--accent-gold-hover));
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .history-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: var(--accent-gold);
        }

        .history-card:hover::before {
            transform: scaleY(1);
        }

        .history-meta {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .history-title {
            font-weight: 600;
            font-size: 1.125rem;
            color: var(--text-dark);
        }

        .history-date {
            font-size: 0.875rem;
            color: #64748b;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .history-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .history-detail {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .history-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .history-value {
            font-weight: 500;
            color: var(--text-dark);
        }

        .history-actions {
            display: flex;
            gap: 0.75rem;
            align-items: center;
        }

        .btn-link {
            color: var(--secondary-blue);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
        }

        .btn-link:hover {
            background: rgba(0, 71, 171, 0.1);
            color: var(--primary-blue);
        }

        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
            color: #64748b;
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: var(--accent-gold);
            color: var(--primary-blue);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem;
            cursor: pointer;
            box-shadow: var(--shadow-md);
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes rotate {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .content {
                margin-left: 0;
                padding: 1rem;
            }

            .mobile-menu-btn {
                display: block;
            }

            .header-title {
                font-size: 1.875rem;
            }

            .upload-zone {
                padding: 2rem 1rem;
            }

            .history-details {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .header-card,
            .upload-section,
            .history-section {
                padding: 1.5rem;
                border-radius: 1rem;
            }

            .upload-zone {
                padding: 1.5rem 1rem;
            }

            .upload-icon {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <div class="flex">
        <aside class="sidebar" id="sidebar">
            <div class="logo-section">
                <div class="logo-title">
                    <i class="fas fa-file-contract"></i>
                    BTK Analyzer
                </div>
                <div class="logo-subtitle">Contract Analysis Pro</div>
            </div>
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="{{ url_for('index') }}" class="nav-link active">
                        <i class="fas fa-home"></i>
                        Accueil
                    </a>
                </div>
                <div class="nav-item">
                    <a href="{{ url_for('history') }}" class="nav-link">
                        <i class="fas fa-history"></i>
                        Historique
                    </a>
                </div>
            </nav>
        </aside>

        <main class="content">
            <header class="header-card">
                <h1 class="header-title">Analyse de Contrats Bancaires V3.0</h1>
                <p class="header-subtitle">
                    <i class="fas fa-robot"></i>
                    Extracteur Ultra-Intelligent avec IA Contextuelle
                </p>
                <div style="margin-top: 1rem; padding: 1rem; background: rgba(0, 71, 171, 0.1); border-radius: 0.75rem; border-left: 4px solid var(--accent-gold);">
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                        <i class="fas fa-sparkles" style="color: var(--accent-gold);"></i>
                        <strong style="color: var(--primary-blue);">Nouvelles Améliorations V3.0</strong>
                    </div>
                    <div style="font-size: 0.875rem; color: #64748b; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 0.5rem;">
                        <div>✅ Détection CIN tunisien améliorée</div>
                        <div>✅ Patterns flexibles pour contrats</div>
                        <div>✅ Analyse contextuelle intelligente</div>
                        <div>✅ Validation adaptative</div>
                        <div>✅ Post-traitement automatique</div>
                        <div>✅ Gestion formats masqués</div>
                    </div>
                </div>
            </header>

            <section class="upload-section">
                <form method="post" action="{{ url_for('upload_file') }}" enctype="multipart/form-data" id="uploadForm">
                    <div class="upload-zone" id="uploadZone">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">Glissez votre fichier PDF ici</div>
                        <div class="upload-subtext">ou cliquez pour sélectionner un fichier (PDF uniquement, max 10MB)</div>
                        <input type="file" name="file" accept=".pdf" class="file-input" id="fileInput" required>
                    </div>
                    <button type="submit" class="btn-primary" id="analyzeBtn">
                        <i class="fas fa-search"></i>
                        <span class="btn-text">Analyser le contrat</span>
                        <div class="loading-spinner"></div>
                    </button>
                </form>
            </section>

            <section class="history-section">
                <h2 class="section-title">
                    <i class="fas fa-clock"></i>
                    Analyses Récentes
                </h2>
                {% if recent_history %}
                    {% for entry in recent_history %}
                        <div class="history-card">
                            <div class="history-meta">
                                <div class="history-title">{{ entry.original_filename }}</div>
                                <div class="history-date">
                                    <i class="fas fa-calendar-alt"></i>
                                    {{ entry.timestamp | format_datetime }}
                                </div>
                            </div>
                            <div class="history-details">
                                <div class="history-detail">
                                    <div class="history-label">Client</div>
                                    <div class="history-value">{{ entry.summary.client_name or 'Non détecté' }}</div>
                                </div>
                                <div class="history-detail">
                                    <div class="history-label">Type de Contrat</div>
                                    <div class="history-value">{{ entry.summary.contract_type or 'Non détecté' }}</div>
                                </div>
                                <div class="history-detail">
                                    <div class="history-label">Numéro Contrat</div>
                                    <div class="history-value">{{ entry.summary.contract_number or 'Non détecté' }}</div>
                                </div>
                                <div class="history-detail">
                                    <div class="history-label">Montant</div>
                                    <div class="history-value">{{ entry.summary.amount or 'Non détecté' }}</div>
                                </div>
                                <div class="history-detail">
                                    <div class="history-label">CIN</div>
                                    <div class="history-value">{{ entry.summary.cin or 'Non détecté' }}</div>
                                </div>
                                <div class="history-detail">
                                    <div class="history-label">Score Qualité</div>
                                    <div class="history-value">
                                        {% set score = entry.summary.global_score or 0 %}
                                        <span style="color: {% if score >= 70 %}#10b981{% elif score >= 50 %}#f59e0b{% else %}#ef4444{% endif %}; font-weight: 600;">
                                            <i class="fas fa-{% if score >= 70 %}check-circle{% elif score >= 50 %}exclamation-triangle{% else %}times-circle{% endif %}"></i>
                                            {{ "%.1f"|format(score) }}%
                                        </span>
                                    </div>
                                </div>
                                <div class="history-detail">
                                    <div class="history-label">Version</div>
                                    <div class="history-value">
                                        <span style="color: var(--accent-gold); font-weight: 600;">
                                            <i class="fas fa-robot"></i>
                                            V{{ entry.summary.version or '1.0' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="history-detail">
                                    <div class="history-label">Champs Extraits</div>
                                    <div class="history-value">{{ entry.summary.total_fields or 0 }} champs</div>
                                </div>
                            </div>
                            <div class="history-actions">
                                <a href="{{ url_for('history_details', analysis_id=entry.id) }}" class="btn-link">
                                    <i class="fas fa-eye"></i>
                                    Voir les détails
                                </a>
                                <a href="{{ url_for('download', filename=entry.result_filename) }}" class="btn-link">
                                    <i class="fas fa-download"></i>
                                    Télécharger
                                </a>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-inbox"></i>
                        </div>
                        <p>Aucune analyse récente disponible</p>
                        <p style="font-size: 0.875rem; margin-top: 0.5rem;">Uploadez votre premier contrat pour commencer</p>
                    </div>
                {% endif %}
            </section>
        </main>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const sidebar = document.getElementById('sidebar');
            const content = document.querySelector('.content');
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const uploadZone = document.getElementById('uploadZone');
            const fileInput = document.getElementById('fileInput');
            const uploadForm = document.getElementById('uploadForm');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const btnText = analyzeBtn.querySelector('.btn-text');
            const loadingSpinner = analyzeBtn.querySelector('.loading-spinner');

            let isOpen = window.innerWidth > 768;

            // Mobile menu toggle
            mobileMenuBtn.addEventListener('click', () => {
                sidebar.classList.toggle('open');
            });

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', (e) => {
                if (window.innerWidth <= 768 &&
                    !sidebar.contains(e.target) &&
                    !mobileMenuBtn.contains(e.target)) {
                    sidebar.classList.remove('open');
                }
            });

            // Responsive sidebar
            window.addEventListener('resize', () => {
                isOpen = window.innerWidth > 768;
                if (isOpen) {
                    sidebar.classList.add('open');
                } else {
                    sidebar.classList.remove('open');
                }
            });

            // File upload drag and drop
            uploadZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadZone.classList.add('dragover');
            });

            uploadZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');
            });

            uploadZone.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    if (file.type === 'application/pdf') {
                        fileInput.files = files;
                        updateUploadZone(file);
                    } else {
                        showNotification('Veuillez sélectionner un fichier PDF', 'error');
                    }
                }
            });

            uploadZone.addEventListener('click', () => {
                fileInput.click();
            });

            fileInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    updateUploadZone(file);
                }
            });

            function updateUploadZone(file) {
                const uploadIcon = uploadZone.querySelector('.upload-icon i');
                const uploadText = uploadZone.querySelector('.upload-text');
                const uploadSubtext = uploadZone.querySelector('.upload-subtext');

                uploadIcon.className = 'fas fa-file-pdf';
                uploadText.textContent = file.name;
                uploadSubtext.textContent = `Taille: ${(file.size / 1024 / 1024).toFixed(2)} MB`;
                uploadZone.style.borderColor = 'var(--accent-gold)';
                uploadZone.style.background = 'rgba(255, 193, 7, 0.05)';
            }

            // Form submission with loading state
            uploadForm.addEventListener('submit', (e) => {
                if (!fileInput.files[0]) {
                    e.preventDefault();
                    showNotification('Veuillez sélectionner un fichier PDF', 'error');
                    return;
                }

                // Show loading state
                analyzeBtn.disabled = true;
                btnText.style.display = 'none';
                loadingSpinner.style.display = 'block';
                analyzeBtn.style.background = 'linear-gradient(135deg, #e0e0e0, #bdbdbd)';

                // Update button text
                setTimeout(() => {
                    btnText.textContent = 'Analyse en cours...';
                    btnText.style.display = 'inline';
                    loadingSpinner.style.display = 'none';
                }, 500);
            });

            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.innerHTML = `
                    <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                `;

                notification.style.cssText = `
                    position: fixed;
                    top: 2rem;
                    right: 2rem;
                    background: ${type === 'error' ? '#fee2e2' : '#dbeafe'};
                    color: ${type === 'error' ? '#dc2626' : '#1d4ed8'};
                    padding: 1rem 1.5rem;
                    border-radius: 0.75rem;
                    box-shadow: var(--shadow-lg);
                    z-index: 1000;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    font-weight: 500;
                    border: 1px solid ${type === 'error' ? '#fecaca' : '#bfdbfe'};
                    transform: translateX(100%);
                    transition: transform 0.3s ease;
                `;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                }, 100);

                setTimeout(() => {
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 3000);
            }

            // Add smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
