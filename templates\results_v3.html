<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Résultats V3.0 - BTK Contract Analyzer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        :root {
            --primary-blue: #003087;
            --secondary-blue: #0047AB;
            --accent-gold: #FFC107;
            --accent-gold-hover: #FFB300;
            --text-light: #ffffff;
            --text-dark: #1a1a1a;
            --bg-light: #f8fafc;
            --border-light: #e2e8f0;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: var(--text-light);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            color: var(--text-dark);
            padding: 2rem;
            border-radius: 1.5rem;
            box-shadow: var(--shadow-xl);
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-title {
            font-size: 2.25rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .header-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #64748b;
            font-size: 0.875rem;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--accent-gold), var(--accent-gold-hover));
            color: var(--primary-blue);
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: rgba(0, 71, 171, 0.1);
            color: var(--secondary-blue);
            border: 1px solid rgba(0, 71, 171, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(0, 71, 171, 0.2);
        }

        .results-grid {
            display: grid;
            gap: 2rem;
            grid-template-columns: 1fr;
        }

        .result-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 1.5rem;
            box-shadow: var(--shadow-xl);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .section-header {
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            color: var(--text-light);
            padding: 1.5rem 2rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .section-header:hover {
            background: linear-gradient(135deg, var(--secondary-blue), var(--primary-blue));
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .section-content {
            padding: 2rem;
            color: var(--text-dark);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .info-item {
            background: var(--bg-light);
            padding: 1.5rem;
            border-radius: 1rem;
            border-left: 4px solid var(--accent-gold);
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .info-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }

        .info-value {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 1rem;
        }

        .score-card {
            background: linear-gradient(135deg, var(--success), #059669);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            text-align: center;
            box-shadow: var(--shadow-lg);
        }

        .score-value {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .score-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .masked-field {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            padding: 1rem;
            border-radius: 0.75rem;
            margin-bottom: 1rem;
        }

        .masked-field-header {
            font-weight: 600;
            color: var(--error);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .toggle-icon {
            transition: transform 0.3s ease;
        }

        .section-content.collapsed {
            display: none;
        }

        .section-header.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header-title {
                font-size: 1.875rem;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .header-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header-card">
            <h1 class="header-title">
                <i class="fas fa-robot"></i>
                Résultats d'Analyse V3.0
            </h1>
            
            <div class="header-meta">
                <div class="meta-item">
                    <i class="fas fa-file-pdf"></i>
                    <span>{{ original_filename }}</span>
                </div>
                {% if results.metadata %}
                <div class="meta-item">
                    <i class="fas fa-cogs"></i>
                    <span>{{ results.metadata.methode_extraction or 'Extracteur Standard' }}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-clock"></i>
                    <span>{{ results.metadata.extraction_timestamp | format_datetime }}</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-file-alt"></i>
                    <span>{{ results.metadata.pages_traitees or 1 }} page(s) traitée(s)</span>
                </div>
                <div class="meta-item">
                    <i class="fas fa-code-branch"></i>
                    <span>Version {{ results.metadata.version or '1.0' }}</span>
                </div>
                {% endif %}
            </div>

            <div class="header-actions">
                <a href="{{ url_for('download', filename=filename) }}" class="btn btn-primary">
                    <i class="fas fa-download"></i>
                    Télécharger JSON
                </a>
                <a href="{{ url_for('index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Retour à l'accueil
                </a>
            </div>
        </header>

        <div class="results-grid">
            <!-- Scores de Qualité V3.0 -->
            {% if results.scores_qualite %}
            <div class="result-section">
                <div class="section-header" onclick="toggleSection('quality')">
                    <div class="section-title">
                        <i class="fas fa-chart-line"></i>
                        Scores de Qualité V3.0
                    </div>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="section-content" id="quality">
                    <div class="info-grid">
                        <div class="score-card" style="background: linear-gradient(135deg, 
                            {% if results.scores_qualite.score_global >= 70 %}var(--success), #059669
                            {% elif results.scores_qualite.score_global >= 50 %}var(--warning), #d97706
                            {% else %}var(--error), #dc2626{% endif %});">
                            <div class="score-value">{{ "%.1f"|format(results.scores_qualite.score_global) }}%</div>
                            <div class="score-label">Score Global</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Champs Banque</div>
                            <div class="info-value">{{ results.scores_qualite.champs_banque_remplis }} / {{ results.scores_qualite.champs_possibles_total // 3 }} ({{ "%.1f"|format(results.scores_qualite.score_banque) }}%)</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Champs Contrat</div>
                            <div class="info-value">{{ results.scores_qualite.champs_contrat_remplis }} / {{ results.scores_qualite.champs_possibles_total // 3 }} ({{ "%.1f"|format(results.scores_qualite.score_contrat) }}%)</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Champs Client</div>
                            <div class="info-value">{{ results.scores_qualite.champs_client_remplis }} / {{ results.scores_qualite.champs_possibles_total // 3 }} ({{ "%.1f"|format(results.scores_qualite.score_client) }}%)</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Total Extrait</div>
                            <div class="info-value">{{ results.scores_qualite.champs_remplis_total }} / {{ results.scores_qualite.champs_possibles_total }} champs</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">Champs Masqués</div>
                            <div class="info-value">{{ results.scores_qualite.champs_masques_detectes }} détectés</div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Informations Bancaires -->
            {% if results.informations_banque %}
            <div class="result-section">
                <div class="section-header" onclick="toggleSection('bank')">
                    <div class="section-title">
                        <i class="fas fa-university"></i>
                        Informations Bancaires
                    </div>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="section-content" id="bank">
                    <div class="info-grid">
                        {% for key, value in results.informations_banque.items() %}
                        {% if value %}
                        <div class="info-item">
                            <div class="info-label">{{ key.replace('_', ' ').title() }}</div>
                            <div class="info-value">{{ value }}</div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Informations Contractuelles -->
            {% if results.informations_contrat %}
            <div class="result-section">
                <div class="section-header" onclick="toggleSection('contract')">
                    <div class="section-title">
                        <i class="fas fa-file-contract"></i>
                        Informations Contractuelles
                    </div>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="section-content" id="contract">
                    <div class="info-grid">
                        {% for key, value in results.informations_contrat.items() %}
                        {% if value %}
                        <div class="info-item" {% if key in ['numero_contrat', 'montant_principal', 'taux_interet'] %}style="border-left-color: var(--accent-gold); border-left-width: 6px;"{% endif %}>
                            <div class="info-label">{{ key.replace('_', ' ').title() }}</div>
                            <div class="info-value">
                                {% if key == 'montant_principal' %}
                                    <i class="fas fa-coins" style="color: var(--accent-gold); margin-right: 0.5rem;"></i>
                                {% elif key == 'taux_interet' %}
                                    <i class="fas fa-percentage" style="color: var(--success); margin-right: 0.5rem;"></i>
                                {% elif key == 'numero_contrat' %}
                                    <i class="fas fa-hashtag" style="color: var(--primary-blue); margin-right: 0.5rem;"></i>
                                {% endif %}
                                {{ value }}
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Informations Client -->
            {% if results.informations_client %}
            <div class="result-section">
                <div class="section-header" onclick="toggleSection('client')">
                    <div class="section-title">
                        <i class="fas fa-user"></i>
                        Informations Client
                    </div>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="section-content" id="client">
                    <div class="info-grid">
                        {% for key, value in results.informations_client.items() %}
                        {% if value %}
                        <div class="info-item" {% if key in ['cin', 'nom', 'prenom'] %}style="border-left-color: var(--success); border-left-width: 6px;"{% endif %}>
                            <div class="info-label">{{ key.replace('_', ' ').title() }}</div>
                            <div class="info-value">
                                {% if key == 'cin' %}
                                    <i class="fas fa-id-card" style="color: var(--success); margin-right: 0.5rem;"></i>
                                {% elif key in ['nom', 'prenom'] %}
                                    <i class="fas fa-user" style="color: var(--primary-blue); margin-right: 0.5rem;"></i>
                                {% elif key == 'telephone_mobile' %}
                                    <i class="fas fa-phone" style="color: var(--warning); margin-right: 0.5rem;"></i>
                                {% endif %}
                                {{ value }}
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Champs Masqués -->
            {% if results.champs_masques %}
            <div class="result-section">
                <div class="section-header" onclick="toggleSection('masked')">
                    <div class="section-title">
                        <i class="fas fa-eye-slash"></i>
                        Champs Masqués Détectés ({{ results.champs_masques|length }})
                    </div>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="section-content" id="masked">
                    {% for field in results.champs_masques %}
                    <div class="masked-field">
                        <div class="masked-field-header">
                            <i class="fas fa-mask"></i>
                            {{ field.champ.replace('_', ' ').title() }}
                        </div>
                        <div style="font-size: 0.875rem; color: #64748b;">
                            <strong>Masque:</strong> {{ field.masque }} ({{ field.longueur_masque }} caractères)<br>
                            <strong>Contexte:</strong> "{{ field.contexte }}"
                            {% if field.methode_detection %}
                            <br><strong>Méthode:</strong> {{ field.methode_detection }}
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Améliorations V3.0 -->
            {% if results.metadata and results.metadata.ameliorations %}
            <div class="result-section">
                <div class="section-header" onclick="toggleSection('improvements')">
                    <div class="section-title">
                        <i class="fas fa-sparkles"></i>
                        Améliorations V3.0
                    </div>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="section-content" id="improvements">
                    <div style="display: grid; gap: 1rem;">
                        {% for improvement in results.metadata.ameliorations %}
                        <div style="background: rgba(16, 185, 129, 0.1); padding: 1rem; border-radius: 0.75rem; border-left: 4px solid var(--success);">
                            <i class="fas fa-check-circle" style="color: var(--success); margin-right: 0.5rem;"></i>
                            {{ improvement }}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <script>
        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId);
            const header = content.previousElementSibling;
            const icon = header.querySelector('.toggle-icon');
            
            content.classList.toggle('collapsed');
            header.classList.toggle('collapsed');
        }

        // Auto-expand first section
        document.addEventListener('DOMContentLoaded', () => {
            const firstSection = document.querySelector('.section-content');
            if (firstSection) {
                firstSection.classList.remove('collapsed');
            }
        });
    </script>
</body>
</html>
