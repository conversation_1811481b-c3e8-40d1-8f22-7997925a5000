<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Historique - BTK Contract Analyzer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        :root {
            --primary-blue: #003087;
            --secondary-blue: #0047AB;
            --accent-gold: #FFC107;
            --accent-gold-hover: #FFB300;
            --text-light: #ffffff;
            --text-dark: #1a1a1a;
            --bg-light: #f8fafc;
            --border-light: #e2e8f0;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: var(--text-light);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .sidebar {
            background: linear-gradient(180deg, var(--secondary-blue) 0%, var(--primary-blue) 100%);
            height: 100vh;
            position: fixed;
            width: 280px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
        }

        .content {
            margin-left: 280px;
            padding: 2rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 100vh;
        }

        .logo-section {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-gold);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .logo-subtitle {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 400;
        }

        .nav-menu {
            padding: 1.5rem 0;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 0.75rem;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            color: var(--text-light);
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(4px);
        }

        .nav-link.active {
            background: var(--accent-gold);
            color: var(--primary-blue);
            font-weight: 600;
        }

        .header-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            color: var(--text-dark);
            padding: 2rem;
            border-radius: 1.5rem;
            box-shadow: var(--shadow-xl);
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-title {
            font-size: 2.25rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-stats {
            display: flex;
            gap: 2rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #64748b;
            font-size: 0.875rem;
        }

        .stat-value {
            font-weight: 600;
            color: var(--text-dark);
        }

        .search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 1.5rem;
            border-radius: 1rem;
            box-shadow: var(--shadow-md);
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .search-container {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 250px;
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-light);
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--accent-gold);
            box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
        }

        .filter-select {
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-light);
            border-radius: 0.5rem;
            font-size: 1rem;
            background: white;
            cursor: pointer;
        }

        .history-container {
            display: grid;
            gap: 1.5rem;
        }

        .history-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 1.5rem;
            box-shadow: var(--shadow-md);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }

        .history-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, var(--accent-gold), var(--accent-gold-hover));
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .history-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .history-card:hover::before {
            transform: scaleY(1);
        }

        .card-header {
            background: linear-gradient(135deg, var(--secondary-blue), var(--primary-blue));
            color: var(--text-light);
            padding: 1.5rem 2rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }

        .card-header:hover {
            background: linear-gradient(135deg, var(--primary-blue), #001f5c);
        }

        .card-title {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.125rem;
            font-weight: 600;
        }

        .card-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 0.875rem;
            opacity: 0.9;
        }

        .card-toggle {
            transition: transform 0.3s ease;
            font-size: 1.25rem;
        }

        .card-header.active .card-toggle {
            transform: rotate(180deg);
        }

        .card-content {
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: #ffffff;
        }

        .card-content.active {
            max-height: 500px;
        }

        .content-inner {
            padding: 2rem;
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .detail-label {
            font-size: 0.75rem;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .detail-value {
            font-weight: 500;
            color: var(--text-dark);
            font-size: 1rem;
        }

        .detail-value.empty {
            color: #94a3b8;
            font-style: italic;
        }

        .card-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid var(--border-light);
        }

        .btn-link {
            color: var(--secondary-blue);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
        }

        .btn-link:hover {
            background: rgba(0, 71, 171, 0.1);
            color: var(--primary-blue);
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 1.5rem;
            box-shadow: var(--shadow-md);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .empty-icon {
            font-size: 4rem;
            color: #cbd5e1;
            margin-bottom: 1.5rem;
        }

        .empty-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .empty-text {
            color: #64748b;
            font-size: 1rem;
        }

        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1001;
            background: var(--accent-gold);
            color: var(--primary-blue);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem;
            cursor: pointer;
            box-shadow: var(--shadow-md);
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .content {
                margin-left: 0;
                padding: 1rem;
            }

            .mobile-menu-btn {
                display: block;
            }

            .header-title {
                font-size: 1.875rem;
            }

            .search-container {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input {
                min-width: auto;
            }

            .details-grid {
                grid-template-columns: 1fr;
            }

            .card-actions {
                flex-direction: column;
                align-items: stretch;
            }
        }

        @media (max-width: 480px) {
            .header-card,
            .search-section,
            .history-card {
                border-radius: 1rem;
            }

            .content-inner {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <div class="flex">
        <aside class="sidebar" id="sidebar">
            <div class="logo-section">
                <div class="logo-title">
                    <i class="fas fa-file-contract"></i>
                    BTK Analyzer
                </div>
                <div class="logo-subtitle">Contract Analysis Pro</div>
            </div>
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="{{ url_for('index') }}" class="nav-link">
                        <i class="fas fa-home"></i>
                        Accueil
                    </a>
                </div>
                <div class="nav-item">
                    <a href="{{ url_for('history') }}" class="nav-link active">
                        <i class="fas fa-history"></i>
                        Historique
                    </a>
                </div>
            </nav>
        </aside>

        <main class="content">
            <header class="header-card">
                <h1 class="header-title">Historique des Analyses V3.0 🚀</h1>
                <div style="margin-bottom: 1.5rem; padding: 1rem; background: rgba(16, 185, 129, 0.1); border-radius: 0.75rem; border-left: 4px solid var(--success);">
                    <div style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.875rem; color: var(--success);">
                        <i class="fas fa-robot"></i>
                        <strong>Extracteur Ultra-Intelligent avec IA Contextuelle et Patterns Flexibles</strong>
                    </div>
                </div>
                <div class="header-stats">
                    <div class="stat-item">
                        <i class="fas fa-file-alt"></i>
                        <span>Total: <span class="stat-value">{{ history | length if history else 0 }}</span> analyses</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Dernière mise à jour: <span class="stat-value">{{ moment().format('DD/MM/YYYY à HH:mm') if moment else '02/07/2025 à 14:05' }}</span></span>
                    </div>
                </div>
            </header>

            {% if history %}
                <div class="search-section">
                    <div class="search-container">
                        <input type="text" id="searchInput" class="search-input" placeholder="Rechercher par nom de fichier, client, ou numéro de contrat...">
                        <select id="filterSelect" class="filter-select">
                            <option value="">Tous les types</option>
                            <option value="credit">Crédit</option>
                            <option value="compte">Compte</option>
                            <option value="epargne">Épargne</option>
                            <option value="assurance">Assurance</option>
                        </select>
                    </div>
                </div>

                <div class="history-container" id="historyContainer">
                    {% for entry in history %}
                        <div class="history-card" data-filename="{{ entry.original_filename | lower }}" data-client="{{ entry.summary.client_name | lower if entry.summary.client_name else '' }}" data-contract="{{ entry.summary.contract_number | lower if entry.summary.contract_number else '' }}" data-type="{{ entry.summary.contract_type | lower if entry.summary.contract_type else '' }}">
                            <div class="card-header" onclick="toggleCard('history-{{ entry.id }}')">
                                <div class="card-title">
                                    <i class="fas fa-file-pdf"></i>
                                    <span>{{ entry.original_filename }}</span>
                                </div>
                                <div class="card-meta">
                                    <span>{{ loop.index }}/{{ history | length }}</span>
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>{{ entry.timestamp | format_datetime }}</span>
                                    <i class="fas fa-chevron-down card-toggle"></i>
                                </div>
                            </div>
                            <div id="history-{{ entry.id }}-content" class="card-content">
                                <div class="content-inner">
                                    <div class="details-grid">
                                        <div class="detail-item">
                                            <div class="detail-label">Date d'analyse</div>
                                            <div class="detail-value">{{ entry.timestamp | format_datetime }}</div>
                                        </div>
                                        <div class="detail-item">
                                            <div class="detail-label">Nom du client</div>
                                            <div class="detail-value {{ 'empty' if not entry.summary.client_name }}">
                                                {{ entry.summary.client_name or 'Non détecté' }}
                                            </div>
                                        </div>
                                        <div class="detail-item">
                                            <div class="detail-label">Type de contrat</div>
                                            <div class="detail-value {{ 'empty' if not entry.summary.contract_type }}">
                                                {{ entry.summary.contract_type or 'Non détecté' }}
                                            </div>
                                        </div>
                                        <div class="detail-item">
                                            <div class="detail-label">Numéro de contrat</div>
                                            <div class="detail-value {{ 'empty' if not entry.summary.contract_number }}">
                                                {{ entry.summary.contract_number or 'Non détecté' }}
                                            </div>
                                        </div>
                                        <div class="detail-item">
                                            <div class="detail-label">Champs masqués</div>
                                            <div class="detail-value {{ 'empty' if not entry.summary.masked_fields }}">
                                                {{ entry.summary.masked_fields or 'Aucun' }}
                                            </div>
                                        </div>
                                        <div class="detail-item">
                                            <div class="detail-label">Statut</div>
                                            <div class="detail-value">
                                                <span style="color: var(--success); font-weight: 600;">
                                                    <i class="fas fa-check-circle"></i>
                                                    Analysé avec succès
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-actions">
                                        <a href="{{ url_for('history_details', analysis_id=entry.id) }}" class="btn-link">
                                            <i class="fas fa-eye"></i>
                                            Voir les détails complets
                                        </a>
                                        <a href="{{ url_for('download', filename=entry.result_filename) }}" class="btn-link">
                                            <i class="fas fa-download"></i>
                                            Télécharger les résultats
                                        </a>
                                        <button onclick="shareAnalysis('{{ entry.id }}')" class="btn-link" style="border: none; background: none;">
                                            <i class="fas fa-share-alt"></i>
                                            Partager
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="empty-title">Aucune analyse dans l'historique</div>
                    <div class="empty-text">Commencez par analyser votre premier contrat pour voir l'historique ici</div>
                </div>
            {% endif %}
        </main>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const sidebar = document.getElementById('sidebar');
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const searchInput = document.getElementById('searchInput');
            const filterSelect = document.getElementById('filterSelect');
            const historyContainer = document.getElementById('historyContainer');

            // Mobile menu toggle
            mobileMenuBtn.addEventListener('click', () => {
                sidebar.classList.toggle('open');
            });

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', (e) => {
                if (window.innerWidth <= 768 &&
                    !sidebar.contains(e.target) &&
                    !mobileMenuBtn.contains(e.target)) {
                    sidebar.classList.remove('open');
                }
            });

            // Responsive sidebar
            window.addEventListener('resize', () => {
                if (window.innerWidth > 768) {
                    sidebar.classList.add('open');
                } else {
                    sidebar.classList.remove('open');
                }
            });

            // Search and filter functionality
            if (searchInput && filterSelect && historyContainer) {
                function filterHistory() {
                    const searchTerm = searchInput.value.toLowerCase();
                    const filterType = filterSelect.value.toLowerCase();
                    const cards = historyContainer.querySelectorAll('.history-card');

                    cards.forEach(card => {
                        const filename = card.dataset.filename || '';
                        const client = card.dataset.client || '';
                        const contract = card.dataset.contract || '';
                        const type = card.dataset.type || '';

                        const matchesSearch = !searchTerm ||
                            filename.includes(searchTerm) ||
                            client.includes(searchTerm) ||
                            contract.includes(searchTerm);

                        const matchesFilter = !filterType || type.includes(filterType);

                        if (matchesSearch && matchesFilter) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });

                    // Show/hide empty state
                    const visibleCards = historyContainer.querySelectorAll('.history-card[style="display: block"], .history-card:not([style*="display: none"])');
                    if (visibleCards.length === 0 && (searchTerm || filterType)) {
                        showNoResults();
                    } else {
                        hideNoResults();
                    }
                }

                searchInput.addEventListener('input', filterHistory);
                filterSelect.addEventListener('change', filterHistory);

                function showNoResults() {
                    let noResults = document.getElementById('noResults');
                    if (!noResults) {
                        noResults = document.createElement('div');
                        noResults.id = 'noResults';
                        noResults.className = 'empty-state';
                        noResults.innerHTML = `
                            <div class="empty-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="empty-title">Aucun résultat trouvé</div>
                            <div class="empty-text">Essayez de modifier vos critères de recherche</div>
                        `;
                        historyContainer.appendChild(noResults);
                    }
                    noResults.style.display = 'block';
                }

                function hideNoResults() {
                    const noResults = document.getElementById('noResults');
                    if (noResults) {
                        noResults.style.display = 'none';
                    }
                }
            }
        });

        function toggleCard(cardId) {
            const header = event.currentTarget;
            const content = document.getElementById(`${cardId}-content`);
            const toggle = header.querySelector('.card-toggle');

            if (content.classList.contains('active')) {
                content.classList.remove('active');
                header.classList.remove('active');
            } else {
                content.classList.add('active');
                header.classList.add('active');
            }
        }

        function shareAnalysis(analysisId) {
            const url = `${window.location.origin}/history/details/${analysisId}`;

            if (navigator.share) {
                navigator.share({
                    title: 'Analyse de contrat BTK',
                    text: 'Consultez cette analyse de contrat',
                    url: url
                }).catch(console.error);
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(url).then(() => {
                    showNotification('Lien copié dans le presse-papiers', 'success');
                }).catch(() => {
                    showNotification('Erreur lors de la copie du lien', 'error');
                });
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            `;

            notification.style.cssText = `
                position: fixed;
                top: 2rem;
                right: 2rem;
                background: ${type === 'error' ? '#fee2e2' : type === 'success' ? '#d1fae5' : '#dbeafe'};
                color: ${type === 'error' ? '#dc2626' : type === 'success' ? '#065f46' : '#1d4ed8'};
                padding: 1rem 1.5rem;
                border-radius: 0.75rem;
                box-shadow: var(--shadow-lg);
                z-index: 1000;
                display: flex;
                align-items: center;
                gap: 0.75rem;
                font-weight: 500;
                border: 1px solid ${type === 'error' ? '#fecaca' : type === 'success' ? '#a7f3d0' : '#bfdbfe'};
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'f':
                        e.preventDefault();
                        document.getElementById('searchInput')?.focus();
                        break;
                    case 'h':
                        e.preventDefault();
                        window.location.href = "{{ url_for('index') }}";
                        break;
                }
            }
        });
    </script>
</body>
</html>