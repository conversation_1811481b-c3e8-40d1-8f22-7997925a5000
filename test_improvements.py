#!/usr/bin/env python3
"""
SCRIPT DE TEST POUR LES AMÉLIORATIONS V3.0
Test des nouvelles fonctionnalités d'extraction intelligente
"""

import os
import json
import logging
from datetime import datetime
from simple_extract_contract import AdvancedContractExtractor

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_pattern_improvements():
    """Test des améliorations de patterns"""
    logger.info("🧪 === TEST DES AMÉLIORATIONS V3.0 ===")
    
    # Textes de test pour les nouveaux patterns
    test_texts = {
        "numero_contrat": [
            "Référence: CDTGAR1_000307_250630_141635",
            "Contrat n° BTK-2024-001234",
            "Dossier: FIN_789456_2024",
            "N° 123456789",
            "Numéro contrat: ABC123DEF456"
        ],
        "cin": [
            "CIN: 01234567",
            "Carte d'identité: 12345678",
            "Document d'identité: 87654321",
            "CIN 09876543",
            "N° CIN: 11223344"
        ],
        "montant": [
            "Montant: 150.000.000 dinars",
            "Somme principale de 1 500 000 DT",
            "Prêt de 2,500,000 TND",
            "Capital emprunté: 750000",
            "Financement: 3.250.500 D"
        ],
        "telephone": [
            "Mobile: +216 98 123 456",
            "GSM: 22 345 678",
            "Téléphone portable: 216 99 876 543",
            "Numéro portable: 20123456",
            "Tel mobile: 98.765.432"
        ],
        "dates": [
            "Edité le: 15/03/2024",
            "Date d'édition: 25-12-2023",
            "Etabli le 10.06.2024",
            "Le 05/11/2023",
            "Date: 30 mars 2024"
        ]
    }
    
    extractor = AdvancedContractExtractor()
    
    # Test de chaque catégorie
    for category, texts in test_texts.items():
        logger.info(f"\n📋 Test {category.upper()}:")
        
        for i, text in enumerate(texts, 1):
            logger.info(f"  Test {i}: {text}")
            
            if category == "numero_contrat":
                result = extractor.extract_contract_info(text)
                found = result.get('numero_contrat', '')
                
            elif category == "cin":
                result = extractor.extract_client_info(text)
                found = result.get('cin', '')
                
            elif category == "montant":
                result = extractor.extract_contract_info(text)
                found = result.get('montant_principal', '')
                
            elif category == "telephone":
                result = extractor.extract_client_info(text)
                found = result.get('telephone_mobile', '')
                
            elif category == "dates":
                result = extractor.extract_contract_info(text)
                found = result.get('date_edition', '')
            
            status = "✅" if found else "❌"
            logger.info(f"    {status} Résultat: '{found}'")

def test_contextual_analysis():
    """Test de l'analyse contextuelle"""
    logger.info("\n🧠 === TEST ANALYSE CONTEXTUELLE ===")
    
    # Texte simulé avec structure de contrat
    test_context = """
    BANQUE TUNISO-KOWEITIENNE
    Société Anonyme au capital de 150.000.000 dinars
    
    CONTRAT DE FINANCEMENT
    Référence: CDTGAR1_000307_250630_141635
    Edité le: 15/03/2024
    
    Montant du prêt: 2.500.000 dinars tunisiens
    Durée: 60 mois
    Taux d'intérêt: 7.5% par an
    
    INFORMATIONS EMPRUNTEUR
    Identifiant: CLI-789456
    Nom de famille: XXXXXXX
    Prénoms: XXXXX
    Naissance: XX/XX/XXXX
    Citoyenneté: XXXXXXXX
    Métier exercé: XXXXXXXXX
    Lieu de résidence: XXXXXXXXXXXXXXX
    Numéro portable: XXXXXXXXX
    Document d'identité: XXXXXXXX
    Compte bancaire: XXXXXXXXXXXX
    """
    
    extractor = AdvancedContractExtractor()
    
    # Test extraction contextuelle
    contract_context = extractor.extract_with_context_analysis(test_context, "contract")
    client_context = extractor.extract_with_context_analysis(test_context, "client")
    
    logger.info("📋 Contexte contrat détecté:")
    for key, value in contract_context.items():
        logger.info(f"  {key}: {value}")
    
    logger.info("👤 Contexte client détecté:")
    for key, value in client_context.items():
        logger.info(f"  {key}: {value}")

def test_data_cleaning():
    """Test du nettoyage des données"""
    logger.info("\n🧹 === TEST NETTOYAGE DONNÉES ===")
    
    extractor = AdvancedContractExtractor()
    
    # Données de test avec problèmes courants
    dirty_data = {
        "contract": {
            "montant_principal": "  2.500.000   dinars tunisiens  ",
            "taux_interet": "7.5 par an",
            "numero_contrat": " CDTGAR1_000307 ",
            "date_edition": "15/03/2024  "
        },
        "client": {
            "nom": "  ben ali  ",
            "cin": "01234567  ",
            "telephone_mobile": "+216-98-123-456",
            "date_naissance": "15/08/1985"
        }
    }
    
    for data_type, data in dirty_data.items():
        logger.info(f"📊 Nettoyage {data_type}:")
        cleaned = extractor.clean_and_validate_data(data, data_type)
        
        for field, value in cleaned.items():
            original = data.get(field, '')
            logger.info(f"  {field}:")
            logger.info(f"    Avant: '{original}'")
            logger.info(f"    Après: '{value}'")

def run_full_test():
    """Test complet sur un fichier PDF"""
    logger.info("\n🚀 === TEST COMPLET SUR PDF ===")
    
    # Chercher un fichier PDF de test
    pdf_folder = 'contrat'
    if not os.path.exists(pdf_folder):
        logger.warning(f"❌ Dossier {pdf_folder} non trouvé")
        return
    
    pdf_files = [f for f in os.listdir(pdf_folder) if f.lower().endswith('.pdf')]
    if not pdf_files:
        logger.warning(f"❌ Aucun PDF trouvé dans {pdf_folder}")
        return
    
    # Prendre le premier PDF
    test_file = pdf_files[0]
    pdf_path = os.path.join(pdf_folder, test_file)
    pdf_name = test_file.rsplit(".", 1)[0]
    
    logger.info(f"📁 Test sur: {test_file}")
    
    extractor = AdvancedContractExtractor()
    
    # Traitement complet
    start_time = datetime.now()
    result = extractor.process_contract(pdf_path, pdf_name)
    processing_time = (datetime.now() - start_time).total_seconds()
    
    # Affichage des résultats
    if "scores_qualite" in result:
        scores = result["scores_qualite"]
        logger.info(f"📊 RÉSULTATS:")
        logger.info(f"  Score global: {scores['score_global']}%")
        logger.info(f"  Champs banque: {scores['champs_banque_remplis']}")
        logger.info(f"  Champs contrat: {scores['champs_contrat_remplis']}")
        logger.info(f"  Champs client: {scores['champs_client_remplis']}")
        logger.info(f"  Temps traitement: {processing_time:.2f}s")
        
        # Sauvegarde du résultat de test
        test_result_path = f"test_results/test_v3_{pdf_name}.json"
        os.makedirs("test_results", exist_ok=True)
        with open(test_result_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        logger.info(f"💾 Résultat sauvé: {test_result_path}")

def main():
    """Fonction principale de test"""
    logger.info("🎯 === TESTS AMÉLIORATIONS EXTRACTEUR V3.0 ===")
    logger.info("🚀 Test des nouvelles fonctionnalités intelligentes\n")
    
    try:
        # Tests unitaires
        test_pattern_improvements()
        test_contextual_analysis()
        test_data_cleaning()
        
        # Test complet
        run_full_test()
        
        logger.info("\n✅ === TESTS TERMINÉS ===")
        logger.info("🎉 Toutes les améliorations ont été testées!")
        
    except Exception as e:
        logger.error(f"❌ Erreur lors des tests: {str(e)}")

if __name__ == "__main__":
    main()
