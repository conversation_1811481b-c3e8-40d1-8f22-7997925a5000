#!/usr/bin/env python3
"""
SCRIPT DE TEST POUR L'INTERFACE WEB V3.0
Test des fonctionnalités web avec l'extracteur amélioré
"""

import os
import json
import requests
import time
from pathlib import Path

def test_web_interface():
    """Test de l'interface web"""
    base_url = "http://localhost:5000"
    
    print("🌐 === TEST INTERFACE WEB V3.0 ===")
    
    # Test 1: Vérifier que le serveur répond
    print("\n1. 🔍 Test de connectivité...")
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("   ✅ Serveur accessible")
            print(f"   📊 Taille de la page: {len(response.content)} bytes")
        else:
            print(f"   ❌ Erreur serveur: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Impossible de se connecter: {e}")
        print("   💡 Assurez-vous que 'python app.py' est en cours d'exécution")
        return False
    
    # Test 2: Vérifier le contenu V3.0
    print("\n2. 🎨 Test du contenu V3.0...")
    if "V3.0" in response.text:
        print("   ✅ Interface V3.0 détectée")
    else:
        print("   ⚠️  Interface V3.0 non détectée")
    
    if "Ultra-Intelligent" in response.text:
        print("   ✅ Branding V3.0 présent")
    else:
        print("   ⚠️  Branding V3.0 manquant")
    
    # Test 3: Vérifier les routes principales
    print("\n3. 🛣️  Test des routes...")
    routes_to_test = [
        ("/", "Page d'accueil"),
        ("/history", "Historique"),
    ]
    
    for route, description in routes_to_test:
        try:
            response = requests.get(f"{base_url}{route}", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {description}: OK")
            else:
                print(f"   ❌ {description}: Erreur {response.status_code}")
        except Exception as e:
            print(f"   ❌ {description}: Exception {e}")
    
    # Test 4: Vérifier les résultats existants
    print("\n4. 📊 Test des résultats existants...")
    results_folder = "results"
    if os.path.exists(results_folder):
        result_files = [f for f in os.listdir(results_folder) if f.endswith('.json')]
        print(f"   📁 {len(result_files)} fichiers de résultats trouvés")
        
        if result_files:
            # Tester l'affichage d'un résultat
            test_file = result_files[0]
            try:
                response = requests.get(f"{base_url}/results/{test_file}", timeout=5)
                if response.status_code == 200:
                    print(f"   ✅ Affichage résultat: {test_file}")
                    
                    # Vérifier si c'est un résultat V3.0
                    if "V3.0" in response.text:
                        print("   🚀 Template V3.0 utilisé")
                    else:
                        print("   📄 Template classique utilisé")
                else:
                    print(f"   ❌ Erreur affichage: {response.status_code}")
            except Exception as e:
                print(f"   ❌ Exception affichage: {e}")
    else:
        print("   📁 Aucun dossier de résultats trouvé")
    
    # Test 5: Vérifier l'historique
    print("\n5. 📚 Test de l'historique...")
    history_file = "analysis_history.json"
    if os.path.exists(history_file):
        try:
            with open(history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
            print(f"   📊 {len(history)} analyses dans l'historique")
            
            # Vérifier les données V3.0
            v3_analyses = [h for h in history if h.get('summary', {}).get('version', '1.0').startswith('3.')]
            print(f"   🚀 {len(v3_analyses)} analyses V3.0")
            
            if v3_analyses:
                sample = v3_analyses[0]['summary']
                print(f"   📋 Exemple V3.0:")
                print(f"      Client: {sample.get('client_name', 'N/A')}")
                print(f"      Score: {sample.get('global_score', 0)}%")
                print(f"      Version: {sample.get('version', 'N/A')}")
                
        except Exception as e:
            print(f"   ❌ Erreur lecture historique: {e}")
    else:
        print("   📁 Aucun historique trouvé")
    
    return True

def test_upload_simulation():
    """Simulation de test d'upload (sans vraiment uploader)"""
    print("\n6. 📤 Test simulation upload...")
    
    # Vérifier les dossiers nécessaires
    folders = ['uploads', 'results', 'contrat']
    for folder in folders:
        if os.path.exists(folder):
            files = os.listdir(folder)
            print(f"   📁 {folder}: {len(files)} fichiers")
        else:
            print(f"   📁 {folder}: Dossier manquant")
    
    # Vérifier les PDFs disponibles
    pdf_folder = 'contrat'
    if os.path.exists(pdf_folder):
        pdf_files = [f for f in os.listdir(pdf_folder) if f.lower().endswith('.pdf')]
        if pdf_files:
            print(f"   📄 {len(pdf_files)} PDFs disponibles pour test")
            print(f"   📋 Exemple: {pdf_files[0]}")
        else:
            print("   📄 Aucun PDF trouvé pour test")
    
    print("   💡 Pour tester l'upload, utilisez l'interface web manuellement")

def generate_test_report():
    """Génère un rapport de test"""
    print("\n📊 === RAPPORT DE TEST ===")
    
    # Informations système
    print(f"🖥️  Système: {os.name}")
    print(f"📁 Répertoire: {os.getcwd()}")
    print(f"🕒 Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Vérifier les fichiers clés
    key_files = [
        'app.py',
        'simple_extract_contract.py',
        'templates/results_v3.html',
        'templates/index.html',
        'templates/history.html'
    ]
    
    print("\n📋 Fichiers clés:")
    for file in key_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   ✅ {file} ({size} bytes)")
        else:
            print(f"   ❌ {file} (manquant)")
    
    # Statistiques des résultats
    if os.path.exists('results'):
        results = [f for f in os.listdir('results') if f.endswith('.json')]
        v3_results = []
        
        for result_file in results:
            try:
                with open(f'results/{result_file}', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                if data.get('metadata', {}).get('version', '1.0').startswith('3.'):
                    v3_results.append(result_file)
            except:
                pass
        
        print(f"\n📊 Statistiques résultats:")
        print(f"   📄 Total: {len(results)} analyses")
        print(f"   🚀 V3.0: {len(v3_results)} analyses")
        print(f"   📈 Ratio V3.0: {len(v3_results)/len(results)*100:.1f}%" if results else "   📈 Ratio V3.0: 0%")

def main():
    """Fonction principale de test"""
    print("🧪 === TESTS INTERFACE WEB V3.0 ===")
    print("🚀 Test des fonctionnalités web améliorées\n")
    
    try:
        # Tests principaux
        if test_web_interface():
            test_upload_simulation()
            generate_test_report()
            
            print("\n✅ === TESTS TERMINÉS ===")
            print("🎉 Interface web V3.0 testée avec succès!")
            print("\n💡 Actions recommandées:")
            print("   1. Testez l'upload d'un PDF via l'interface")
            print("   2. Vérifiez l'affichage des résultats V3.0")
            print("   3. Explorez l'historique enrichi")
            print("   4. Testez la responsivité sur mobile")
        else:
            print("\n❌ === TESTS ÉCHOUÉS ===")
            print("🔧 Vérifiez que l'application web est démarrée")
            
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrompus par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur lors des tests: {str(e)}")

if __name__ == "__main__":
    main()
