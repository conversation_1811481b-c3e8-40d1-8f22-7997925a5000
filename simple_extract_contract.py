#!/usr/bin/env python3
"""
EXTRACTEUR DE CONTRATS ULTRA-INTELLIGENT - VERSION 3.0
Extraction ultra-optimisée avec IA contextuelle et patterns flexibles

AMÉLIORATIONS V3.0:
✅ Patterns ultra-flexibles pour numéros de contrat (CDTGAR1_000307, etc.)
✅ Détection améliorée des CIN tunisiens avec gestion du masquage
✅ Reconnaissance contextuelle intelligente des champs
✅ Validation adaptative et nettoyage automatique
✅ Gestion avancée des montants avec séparateurs
✅ Détection multi-format pour dates et taux
✅ Post-traitement intelligent des données
"""

import os
import json
import pytesseract
from PIL import Image
from pdf2image import convert_from_path
import re
import cv2
import numpy as np
from datetime import datetime
import logging
from typing import Dict, List, Any

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration OCR
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Dossiers
PDF_FOLDER = 'contrat'
IMG_FOLDER = 'images'
TEXT_FOLDER = 'ocr_texts'
RESULT_FOLDER = 'results'

class AdvancedContractExtractor:
    """Extracteur avancé pour contrats avec classification intelligente"""
    
    def __init__(self):
        self.ocr_config = r'--oem 3 --psm 6'
        self.confidence_threshold = 0.7
        
    def extract_text_from_pdf(self, pdf_path: str, pdf_name: str) -> str:
        """Extraction OCR optimisée"""
        logger.info(f"📄 Extraction OCR pour {pdf_name}")
        
        os.makedirs(IMG_FOLDER, exist_ok=True)
        os.makedirs(TEXT_FOLDER, exist_ok=True)
        
        pages = convert_from_path(pdf_path, dpi=300, fmt='PNG')
        all_text = ""
        
        for i, page in enumerate(pages):
            page_num = i + 1
            logger.info(f"🔍 Page {page_num}/{len(pages)}...")
            
            enhanced_page = self.enhance_image_advanced(page)
            
            img_path = f"{IMG_FOLDER}/{pdf_name}_page_{page_num}.png"
            enhanced_page.save(img_path, "PNG")
            
            text = pytesseract.image_to_string(enhanced_page, lang='fra+eng', config=self.ocr_config)
            
            text_path = f"{TEXT_FOLDER}/{pdf_name}_page_{page_num}.txt"
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(text)
            
            all_text += f"\n--- Page {page_num} ---\n{text}"
        
        return self.clean_text_advanced(all_text)
    
    def enhance_image_advanced(self, image: Image.Image) -> Image.Image:
        """Amélioration avancée de l'image"""
        if image.mode != 'L':
            image = image.convert('L')
        
        img_array = np.array(image)
        
        # Débruitage adaptatif
        denoised = cv2.fastNlMeansDenoising(img_array, None, 10, 7, 21)
        
        # Amélioration du contraste adaptatif
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)
        
        # Binarisation optimisée
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Morphologie pour nettoyer
        kernel = np.ones((1,1), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return Image.fromarray(cleaned)
    
    def clean_text_advanced(self, text: str) -> str:
        """Nettoyage avancé du texte OCR"""
        if not text:
            return ""

        # Corrections OCR spécialisées
        corrections = {
            r'[|]': 'I',
            r'[0O](?=[A-Z])': '0',
            r'(?<=[0-9])[O](?=[0-9])': '0',
            r'[1l](?=[0-9]{2,})': '1',
            r'(?<=[0-9])[l](?=[0-9])': '1',
            r'rn': 'm',
            r'cl': 'd',
            r'[àáâãäå]': 'a',
            r'[èéêë]': 'e',
            r'[ìíîï]': 'i',
            r'[òóôõö]': 'o',
            r'[ùúûü]': 'u',
            r'[ç]': 'c',
            r'[ñ]': 'n'
        }
        
        for pattern, replacement in corrections.items():
            text = re.sub(pattern, replacement, text)
        
        # Nettoyage des espaces et formatage
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[\r\n]+', '\n', text)
        text = re.sub(r'\n\s*\n', '\n', text)
        
        return text.strip()

    def extract_bank_info(self, text: str) -> Dict[str, Any]:
        """Extraction spécialisée des informations bancaires"""
        logger.info("🏛️ Extraction informations bancaires...")
        
        bank_info = {
            "nom_banque": "",
            "forme_juridique": "",
            "capital_social": "",
            "numero_rc": "",
            "numero_tva": "",
            "adresse_siege": "",
            "telephone": "",
            "fax": "",
            "email": "",
            "site_web": "",
            "code_banque": "",
            "identifiant_unique": ""
        }
        
        # Patterns optimisés pour informations bancaires
        bank_patterns = {
            "nom_banque": [
                r'BANQUE\s+TUNISO[\-\s]?KOWEITIENNE',
                r'Banque\s+Tuniso[\-\s]?Koweitienne',
                r'BTK(?:\s|$)',
                r'B\.T\.K\.?'
            ],
            "forme_juridique": [
                r'(S\.A\.?)(?:\s|$)',
                r'(Société\s+Anonyme)',
                r'(SARL)',
                r'Forme\s+juridique\s*:?\s*([A-Z\.]+)'
            ],
            "capital_social": [
                r'capital\s+(?:social\s+)?de\s+([0-9\s,\.]+)\s*(?:dinars?|DT|D)',
                r'au\s+capital\s+de\s+([0-9\s,\.]+)',
                r'Capital\s*:?\s*([0-9\s,\.]+)\s*(?:dinars?|DT|D)',
                r'([0-9]{3}[\s,\.][0-9]{3}[\s,\.][0-9]{3})\s*(?:dinars?|DT|D)'
            ],
            "numero_rc": [
                r'R\.?C\.?\s*[=:]\s*(B[0-9]+)',
                r'RC\s*[=:]\s*(B[0-9]+)',
                r'Registre.*?Commerce.*?([B][0-9]{9,12})',
                r'\b(B[0-9]{9,12})\b'
            ],
            "numero_tva": [
                r'TVA\s*[=:]\s*([0-9]+)',
                r'T\.V\.A\s*[=:]\s*([0-9]+)',
                r'Numéro.*?TVA.*?([0-9]{5,})',
                r'Identifiant.*?TVA.*?([0-9]{5,})'
            ],
            "adresse_siege": [
                r'([0-9]+\s+bis,?\s+(?:Av\.?|Avenue)\s+Mohamed\s+V[^|]{10,100})',
                r'(Avenue\s+Mohamed\s+V.*?(?:Tunis|1001))',
                r'([0-9]+\s+bis.*?(?:Tunis|1001|B\.P\.))',
                r'Siège\s+social\s*:?\s*([^|]{20,100})',
                r'Adresse\s*:?\s*([0-9]+.*?(?:Tunis|1001))'
            ],
            "telephone": [
                r'Tél\s*:?\s*(?:\(\+216\))?\s*([0-9\s\.]{8,})',
                r'Téléphone\s*:?\s*(?:\(\+216\))?\s*([0-9\s\.]{8,})',
                r'Tel\s*:?\s*(?:\(\+216\))?\s*([0-9\s\.]{8,})'
            ],
            "fax": [
                r'Fax\s*:?\s*(?:\(\+216\))?\s*([0-9\s\.]{8,})',
                r'Télécopie\s*:?\s*(?:\(\+216\))?\s*([0-9\s\.]{8,})'
            ],
            "site_web": [
                r'(www\.btknet\.com)',
                r'Site.*?(www\.btknet\.com)',
                r'Internet\s*:?\s*(www\.[a-z\.]+)'
            ],
            "identifiant_unique": [
                r'Identifiant\s+Unique\s+n°\s*([0-9]+)',
                r'ID\s+Unique\s*:?\s*([0-9]+)',
                r'Code\s+Identifiant\s*:?\s*([0-9]+)'
            ]
        }
        
        # Extraction avec validation
        for field, patterns in bank_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    value = match.group(1).strip() if match.groups() else match.group(0).strip()
                    if value and self.validate_bank_field(value, field):
                        bank_info[field] = value
                        break
                if bank_info[field]:
                    break
        
        return bank_info

    def extract_contract_info(self, text: str) -> Dict[str, Any]:
        """Extraction spécialisée des informations contractuelles avec patterns améliorés"""
        logger.info("📋 Extraction informations contractuelles...")

        contract_info = {
            "type_contrat": "",
            "numero_contrat": "",
            "date_edition": "",
            "date_signature": "",
            "date_echeance": "",
            "montant_principal": "",
            "devise": "",
            "duree": "",
            "taux_interet": "",
            "taux_effectif_global": "",
            "commission_gestion": "",
            "commission_engagement": "",
            "frais_dossier": "",
            "garanties": "",
            "modalites_remboursement": "",
            "periodicite": "",
            "objet_financement": ""
        }

        # Patterns ultra-optimisés pour informations contractuelles
        contract_patterns = {
            "type_contrat": [
                # Patterns pour types de contrats plus flexibles
                r'CONTRAT\s+DE\s+(PRET|PRÊT|CREDIT|CRÉDIT|FINANCEMENT)',
                r'Convention\s+de\s+([A-Za-zÀ-ÿ\s]{3,30})',
                r'(CONTRAT\s+DE\s+(?:PRET|PRÊT|CREDIT|CRÉDIT|FINANCEMENT))',
                r'Type\s+(?:de\s+)?contrat\s*[=:]?\s*([A-ZÀ-Ÿ\s]{3,40})',
                r'Nature\s+(?:du\s+)?contrat\s*[=:]?\s*([A-ZÀ-Ÿ\s]{3,40})',
                r'(FINANCEMENT[A-Z\s]*)',
                r'Objet\s*[=:]?\s*(PRET|PRÊT|CREDIT|CRÉDIT|FINANCEMENT)',
                r'(?:^|\n)\s*([A-Z]{3,}(?:\s+[A-Z]{3,})*)\s*(?=\n|\r)',  # Lignes en majuscules
                r'CONVENTION\s+([A-ZÀ-Ÿ\s]{5,30})'
            ],
            "numero_contrat": [
                # Patterns améliorés pour numéros de contrat
                r'Numéro.*?(?:contrat|prêt|référence)\s*[=:]\s*([A-Z0-9\-\/\_]{3,20})',
                r'Référence\s*[=:]\s*([A-Z0-9\-\/\_]{3,20})',
                r'N°.*?(?:contrat|prêt|dossier)\s*[=:]?\s*([A-Z0-9\-\/\_]{3,20})',
                r'Contrat\s+(?:n°|numéro|num)\s*[=:]?\s*([A-Z0-9\-\/\_]{3,20})',
                r'(?:Dossier|Ref|REF)\s*[=:]?\s*([A-Z0-9\-\/\_]{6,20})',
                r'Identifiant.*?contrat\s*[=:]?\s*([A-Z0-9\-\/\_]{3,20})',
                # Patterns contextuels
                r'(?:^|\n)\s*(?:Contrat|Référence|N°|Numéro)\s*[=:]?\s*([A-Z0-9\-\/\_]{6,20})',
                # Patterns pour codes spécifiques (ex: CDTGAR1_000307)
                r'([A-Z]{3,8}[0-9\_\-]{6,15})',
                r'([0-9]{6,12})',  # Numéros purement numériques
                # Pattern pour détecter dans le contexte
                r'(?:contrat|prêt|financement).*?([A-Z0-9\-\/\_]{6,20})',
                r'([A-Z0-9]{3,8}[\-\_][0-9]{6,12})'
            ],
            "date_edition": [
                # Patterns améliorés pour dates
                r'Edité\s+le\s*[=:]?\s*([0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{2,4})',
                r'Date\s+(?:d\')?édition\s*[=:]?\s*([0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{2,4})',
                r'Etabli\s+le\s*[=:]?\s*([0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{2,4})',
                r'Date\s*[=:]?\s*([0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{2,4})',
                r'Le\s+([0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{2,4})',
                r'([0-9]{1,2}[\/\-\.][0-9]{1,2}[\/\-\.][0-9]{4})',  # Format simple
                # Formats avec mois en lettres
                r'([0-9]{1,2}\s+(?:janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)\s+[0-9]{4})',
                r'(?:du\s+)?([0-9]{1,2}[\/\-\.][0-9]{1,2}[\/\-\.][0-9]{4})'
            ],
            "montant_principal": [
                # Patterns ultra-flexibles pour montants
                r'somme\s+principale\s+(?:de\s+)?([0-9\s,\.]+)\s*(?:dinars?|DT|D|TND)',
                r'montant\s+(?:total|du\s+prêt|principal).*?([0-9\s,\.]+)\s*(?:dinars?|DT|D|TND)',
                r'prêt\s+de\s+([0-9\s,\.]+)\s*(?:dinars?|DT|D|TND)',
                r'Capital\s+emprunté\s*[=:]?\s*([0-9\s,\.]+)',
                r'Montant\s*[=:]?\s*([0-9\s,\.]+)\s*(?:dinars?|DT|D|TND)',
                # Patterns pour gros montants avec séparateurs
                r'([0-9]{1,3}(?:[\s,\.][0-9]{3})*(?:[\s,\.][0-9]{1,3})?)\s*(?:dinars?|DT|D|TND)',
                r'Somme\s*[=:]?\s*([0-9\s,\.]+)',
                r'Crédit\s+de\s+([0-9\s,\.]+)',
                # Pattern pour montants en chiffres uniquement
                r'([0-9]{6,12})\s*(?:dinars?|DT|D|TND)?',
                r'Financement\s*[=:]?\s*([0-9\s,\.]+)'
            ],
            "duree": [
                # Patterns améliorés pour durée
                r'durée\s+de\s+([0-9]+\s+(?:mois|ans?|années?))',
                r'consenti\s+pour.*?([0-9]+\s+(?:mois|ans?|années?))',
                r'période\s+de\s+([0-9]+\s+(?:mois|ans?|années?))',
                r'Durée\s*[=:]?\s*([0-9]+\s+(?:mois|ans?|années?))',
                r'sur\s+([0-9]+\s+(?:mois|ans?|années?))',
                r'remboursable\s+en\s+([0-9]+\s+(?:mois|ans?|années?))',
                r'([0-9]+)\s+(?:mois|ans?|années?)',  # Pattern simple
                r'Terme\s*[=:]?\s*([0-9]+\s+(?:mois|ans?|années?))'
            ],
            "taux_interet": [
                # Patterns ultra-flexibles pour taux d'intérêt
                r'taux\s+(?:d\'intérêt\s+)?(?:annuel\s+)?(?:de\s+)?([0-9,\.]+\s*%)',
                r'intérêts\s+au\s+taux\s+(?:de\s+)?([0-9,\.]+\s*%)',
                r'Taux\s*[=:]?\s*([0-9,\.]+\s*%)',
                r'([0-9,\.]+)\s*%\s*(?:par\s+an|annuel|l\'an)',
                r'taux\s+(?:nominal|effectif).*?([0-9,\.]+\s*%)',
                r'intérêt.*?([0-9,\.]+\s*%)',
                r'au\s+taux\s+de\s+([0-9,\.]+\s*%)',
                r'([0-9]{1,2}[,\.][0-9]{1,2})\s*%',  # Pattern numérique simple
                r'Taux\s+d\'intérêt\s*[=:]?\s*([0-9,\.]+\s*%)'
            ],
            "taux_effectif_global": [
                # Patterns pour TEG
                r'taux\s+effectif\s+global.*?([0-9,\.]+\s*%)',
                r'TEG\s*[=:]?\s*([0-9,\.]+\s*%)',
                r'T\.E\.G\.?\s*[=:]?\s*([0-9,\.]+\s*%)',
                r'effectif\s+global.*?([0-9,\.]+\s*%)'
            ],
            "commission_gestion": [
                # Patterns pour commissions
                r'commission\s+de\s+gestion.*?([0-9,\.]+\s*%)',
                r'frais\s+de\s+gestion.*?([0-9,\.]+\s*%)',
                r'commission.*?gestion.*?([0-9,\.]+)',
                r'gestion.*?([0-9,\.]+\s*%)'
            ],
            "garanties": [
                # Patterns améliorés pour garanties
                r'garanties?\s*[=:]?\s*([^.\n]{10,200})',
                r'caution.*?([^.\n]{10,200})',
                r'Sûretés\s*[=:]?\s*([^.\n]{10,200})',
                r'Hypothèque.*?([^.\n]{10,200})',
                r'Nantissement.*?([^.\n]{10,200})',
                r'Gage.*?([^.\n]{10,200})',
                r'garantie.*?demandée.*?([^.\n]{10,200})'
            ],
            "devise": [
                # Patterns pour devise
                r'(dinars?\s+tunisiens?|DT|TND|D)',
                r'devise\s*[=:]?\s*([A-Z]{2,4})',
                r'monnaie\s*[=:]?\s*([A-Z]{2,4})'
            ]
        }
        
        # Extraction avec validation
        for field, patterns in contract_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    value = match.group(1).strip() if match.groups() else match.group(0).strip()
                    if value and self.validate_contract_field(value, field):
                        contract_info[field] = value
                        break
                if contract_info[field]:
                    break
        
        return contract_info

    def extract_client_info(self, text: str) -> Dict[str, Any]:
        """Extraction spécialisée des informations client"""
        logger.info("👤 Extraction informations client...")
        
        client_info = {
            "code_client": "",
            "nom": "",
            "prenom": "",
            "nom_complet": "",
            "date_naissance": "",
            "lieu_naissance": "",
            "nationalite": "",
            "situation_familiale": "",
            "nombre_enfants": "",
            "profession": "",
            "secteur_activite": "",
            "employeur": "",
            "revenus_mensuels": "",
            "adresse_domicile": "",
            "ville": "",
            "code_postal": "",
            "pays": "",
            "telephone_fixe": "",
            "telephone_mobile": "",
            "email": "",
            "cin": "",
            "passeport": "",
            "permis_conduire": "",
            "numero_compte": "",
            "agence": ""
        }
        
        # Patterns ultra-optimisés pour informations client
        client_patterns = {
            "code_client": [
                # Patterns améliorés pour code client
                r'Code\s+Client\s*[=:]?\s*([A-Z0-9X\-]{3,20})',
                r'Client\s+(?:n°|numéro|num)\s*[=:]?\s*([A-Z0-9\-]{3,20})',
                r'Identifiant\s+(?:client|emprunteur)\s*[=:]?\s*([A-Z0-9\-]{3,20})',
                r'ID\s+Client\s*[=:]?\s*([A-Z0-9\-]{3,20})',
                r'Référence\s+client\s*[=:]?\s*([A-Z0-9\-]{3,20})',
                # Pattern contextuel pour CLI-789456
                r'(CLI[\-\_][0-9]{6,12})',
                r'([A-Z]{2,4}[\-\_][0-9]{6,12})'
            ],
            "nom": [
                # Patterns flexibles pour nom
                r'Nom\s+(?:du\s+)?(?:Client|Emprunteur|de\s+famille)\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-\']{1,50})',
                r'Nom\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-\']{1,50})',
                r'Emprunteur\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-\']{1,50})',
                r'Nom\s+de\s+famille\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-\']{1,50})',
                r'Famille\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-\']{1,50})',
                # Pattern pour détecter même avec masquage partiel
                r'(?:Nom|Famille).*?([A-ZÀ-Ÿ]{2,}(?:\s+[A-ZÀ-Ÿ]{2,})*)'
            ],
            "prenom": [
                # Patterns flexibles pour prénom
                r'Prénom\s+(?:du\s+)?(?:Client|Emprunteur)\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-\']{1,50})',
                r'Prénom\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-\']{1,50})',
                r'Prénoms\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-\']{1,50})',
                r'Premier\s+nom\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-\']{1,50})'
            ],
            "date_naissance": [
                # Patterns ultra-flexibles pour date de naissance
                r'Date\s+de\s+Naissance\s*[=:]?\s*([0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{2,4})',
                r'Né(?:e)?\s+le\s*[=:]?\s*([0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{2,4})',
                r'Naissance\s*[=:]?\s*([0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{2,4})',
                r'Date\s+naissance\s*[=:]?\s*([0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{2,4})',
                r'([0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{4})',  # Pattern simple
                # Pattern pour format avec masquage XX/XX/XXXX
                r'([X0-9]{1,2}[\/\-\.][X0-9]{1,2}[\/\-\.][X0-9]{2,4})'
            ],
            "nationalite": [
                # Patterns pour nationalité
                r'Nationalité\s*[=:]?\s*([A-ZÀ-Ÿ]{3,30})',
                r'Nat\.\s*[=:]?\s*([A-ZÀ-Ÿ]{3,30})',
                r'Citoyenneté\s*[=:]?\s*([A-ZÀ-Ÿ]{3,30})',
                r'Pays\s+(?:de\s+)?nationalité\s*[=:]?\s*([A-ZÀ-Ÿ]{3,30})',
                r'(TUNISIENNE?|FRANÇAISE?|ALGÉRIENNE?|MAROCAINE?)',  # Nationalités courantes
                r'Origine\s*[=:]?\s*([A-ZÀ-Ÿ]{3,30})'
            ],
            "situation_familiale": [
                # Patterns pour situation familiale
                r'Situation\s+Familiale\s*[=:]?\s*([A-Za-zÀ-ÿ]{3,30})',
                r'Régime\s+Matrimonial\s*[=:]?\s*([A-Za-zÀ-ÿ]{3,30})',
                r'Etat\s+civil\s*[=:]?\s*([A-Za-zÀ-ÿ]{3,30})',
                r'Statut\s+matrimonial\s*[=:]?\s*([A-Za-zÀ-ÿ]{3,30})',
                r'(CÉLIBATAIRE|MARIÉ|DIVORCÉ|VEUF)',  # États civils courants
                r'Civil\s*[=:]?\s*([A-Za-zÀ-ÿ]{3,30})'
            ],
            "profession": [
                # Patterns améliorés pour profession
                r'Profession\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-]{1,60})',
                r'Emploi\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-]{1,60})',
                r'Activité\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-]{1,60})',
                r'Métier\s+exercé\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-]{1,60})',
                r'Fonction\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-]{1,60})',
                r'Travail\s*[=:]?\s*([A-ZÀ-Ÿ][A-ZÀ-Ÿ\s\-]{1,60})'
            ],
            "telephone_mobile": [
                # Patterns ultra-flexibles pour téléphone mobile
                r'Mobile\s*[=:]?\s*([0-9X\s\.\-\+\(\)]{8,25})',
                r'Téléphone\s+(?:mobile|portable)\s*[=:]?\s*([0-9X\s\.\-\+\(\)]{8,25})',
                r'GSM\s*[=:]?\s*([0-9X\s\.\-\+\(\)]{8,25})',
                r'Portable\s*[=:]?\s*([0-9X\s\.\-\+\(\)]{8,25})',
                r'Numéro\s+portable\s*[=:]?\s*([0-9X\s\.\-\+\(\)]{8,25})',
                r'Tel\s+mobile\s*[=:]?\s*([0-9X\s\.\-\+\(\)]{8,25})',
                # Patterns pour formats tunisiens
                r'(\+216\s*[0-9X\s\.\-]{8,15})',
                r'(216\s*[0-9X\s\.\-]{8,15})',
                r'([0-9X]{2}\s*[0-9X]{3}\s*[0-9X]{3})',  # Format XX XXX XXX
                r'([0-9X]{8,12})'  # Format simple
            ],
            "telephone_fixe": [
                # Patterns pour téléphone fixe
                r'Téléphone\s+fixe\s*[=:]?\s*([0-9X\s\.\-\+\(\)]{8,25})',
                r'Tel\s+fixe\s*[=:]?\s*([0-9X\s\.\-\+\(\)]{8,25})',
                r'Fixe\s*[=:]?\s*([0-9X\s\.\-\+\(\)]{8,25})',
                r'Ligne\s+téléphonique\s*[=:]?\s*([0-9X\s\.\-\+\(\)]{8,25})'
            ],
            "adresse_domicile": [
                # Patterns améliorés pour adresse
                r'Adresse\s*[=:]?\s*([^|\n]{15,150})',
                r'Domicile\s*[=:]?\s*([^|\n]{15,150})',
                r'Résidence\s*[=:]?\s*([^|\n]{15,150})',
                r'Lieu\s+de\s+résidence\s*[=:]?\s*([^|\n]{15,150})',
                r'Adresse\s+personnelle\s*[=:]?\s*([^|\n]{15,150})',
                r'Habitation\s*[=:]?\s*([^|\n]{15,150})'
            ],
            "cin": [
                # Patterns ultra-optimisés pour CIN tunisien
                r'CIN\s*[=:]?\s*([0-9X]{8})',
                r'Carte\s+d\'identité\s*[=:]?\s*([0-9X]{8})',
                r'N°\s+CIN\s*[=:]?\s*([0-9X]{8})',
                r'Numéro\s+CIN\s*[=:]?\s*([0-9X]{8})',
                r'Document\s+d\'identité\s*[=:]?\s*([0-9X]{8})',
                r'Identité\s+nationale\s*[=:]?\s*([0-9X]{8})',
                r'Carte\s+nationale\s*[=:]?\s*([0-9X]{8})',
                # Pattern pour format avec masquage partiel
                r'([0-9X]{8})',  # Pattern simple pour 8 chiffres/X
                r'CIN.*?([0-9]{8})',  # Pattern contextuel
                r'identité.*?([0-9X]{8})'
            ],
            "numero_compte": [
                # Patterns pour numéro de compte
                r'Compte\s*(?:n°|numéro|num)?\s*[=:]?\s*([0-9X\-]{10,25})',
                r'N°\s+Compte\s*[=:]?\s*([0-9X\-]{10,25})',
                r'Numéro\s+de\s+compte\s*[=:]?\s*([0-9X\-]{10,25})',
                r'Compte\s+bancaire\s*[=:]?\s*([0-9X\-]{10,25})',
                r'RIB\s*[=:]?\s*([0-9X\-]{10,25})',
                r'IBAN\s*[=:]?\s*([A-Z0-9X\-]{15,35})',
                # Pattern pour formats masqués
                r'([0-9X]{10,25})',  # Pattern simple
                r'compte.*?([0-9X\-]{10,25})'
            ],
            "agence": [
                # Patterns pour agence
                r'Agence\s*[=:]?\s*([A-ZÀ-Ÿ0-9\s\-]{3,50})',
                r'Succursale\s*[=:]?\s*([A-ZÀ-Ÿ0-9\s\-]{3,50})',
                r'Bureau\s*[=:]?\s*([A-ZÀ-Ÿ0-9\s\-]{3,50})',
                r'Agence\s+bancaire\s*[=:]?\s*([A-ZÀ-Ÿ0-9\s\-]{3,50})'
            ]
        }
        
        # Extraction avec validation
        for field, patterns in client_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    value = match.group(1).strip() if match.groups() else match.group(0).strip()
                    if value and self.validate_client_field(value, field):
                        client_info[field] = value
                        break
                if client_info[field]:
                    break
        
        return client_info

    def validate_bank_field(self, value: str, field: str) -> bool:
        """Validation spécialisée pour champs bancaires"""
        if not value or len(value.strip()) < 1:
            return False
            
        # Validations spécifiques
        if field == "capital_social" and not re.match(r'^[0-9\s,\.]+$', value.replace(' ', '')):
            return False
        if field == "numero_rc" and not re.match(r'^B[0-9]{9,12}$', value):
            return False
        if field == "numero_tva" and not re.match(r'^[0-9]{5,}$', value):
            return False
        if field == "telephone" and not re.match(r'^[0-9\s\.\-\+\(\)]{6,20}$', value):
            return False
            
        return True

    def validate_contract_field(self, value: str, field: str) -> bool:
        """Validation spécialisée améliorée pour champs contractuels"""
        if not value or len(value.strip()) < 1:
            return False

        value = value.strip()

        # Validations spécifiques améliorées
        if field == "montant_principal":
            # Accepter les montants avec séparateurs et devises
            if not re.match(r'^[0-9\s,\.]+(?:\s*(?:dinars?|DT|D|TND))?$', value, re.IGNORECASE):
                return False
            # Vérifier que ce n'est pas trop petit (éviter les faux positifs)
            numbers = re.findall(r'[0-9]+', value.replace(',', '').replace('.', ''))
            if numbers and int(''.join(numbers)) < 1000:  # Montant minimum 1000
                return False

        elif field == "taux_interet":
            # Accepter différents formats de taux
            if not re.match(r'^[0-9,\.]+\s*%?(?:\s*(?:par\s+an|annuel|l\'an))?$', value, re.IGNORECASE):
                return False
            # Vérifier que le taux est raisonnable (0-50%)
            taux_num = re.findall(r'[0-9,\.]+', value)
            if taux_num:
                try:
                    taux = float(taux_num[0].replace(',', '.'))
                    if taux < 0 or taux > 50:
                        return False
                except ValueError:
                    return False

        elif field == "date_edition":
            # Accepter différents formats de date, y compris avec X
            if not re.match(r'^[0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{2,4}$', value):
                return False

        elif field == "numero_contrat":
            # Accepter différents formats de numéro de contrat
            if len(value) < 3 or len(value) > 25:
                return False
            # Éviter les valeurs trop génériques
            if value.lower() in ['contrat', 'numero', 'reference', 'n°']:
                return False

        elif field == "duree":
            # Vérifier que la durée contient bien une unité de temps
            if not re.search(r'(?:mois|ans?|années?)', value, re.IGNORECASE):
                return False

        elif field == "type_contrat":
            # Éviter les valeurs trop courtes ou génériques
            if len(value) < 3:
                return False
            if value.lower() in ['type', 'contrat', 'de']:
                return False

        return True

    def validate_client_field(self, value: str, field: str) -> bool:
        """Validation spécialisée améliorée pour champs client"""
        if not value or len(value.strip()) < 1:
            return False

        value = value.strip()

        # Validations spécifiques améliorées
        if field == "cin":
            # CIN tunisien : 8 chiffres, peut contenir des X pour masquage
            if not re.match(r'^[0-9X]{8}$', value):
                return False
            # Éviter les CIN entièrement masqués ou invalides
            if value == 'XXXXXXXX' or value == '00000000':
                return False

        elif field == "date_naissance":
            # Accepter différents formats de date avec masquage
            if not re.match(r'^[0-9X]{1,2}[\/\-\.][0-9X]{1,2}[\/\-\.][0-9X]{2,4}$', value):
                return False
            # Éviter les dates entièrement masquées
            if re.match(r'^X+[\/\-\.]X+[\/\-\.]X+$', value):
                return False

        elif field in ["telephone_mobile", "telephone_fixe"]:
            # Validation flexible pour téléphones
            if not re.match(r'^[0-9X\s\.\-\+\(\)]{6,25}$', value):
                return False
            # Vérifier qu'il y a au moins quelques chiffres
            if len(re.findall(r'[0-9]', value)) < 3:
                return False

        elif field == "code_client":
            # Code client : format flexible
            if len(value) < 3 or len(value) > 20:
                return False
            # Éviter les valeurs génériques
            if value.lower() in ['client', 'code', 'identifiant']:
                return False

        elif field in ["nom", "prenom"]:
            # Validation pour noms et prénoms
            if len(value) < 2:
                return False
            # Éviter les valeurs entièrement masquées courtes
            if re.match(r'^X+$', value) and len(value) < 3:
                return False
            # Éviter les mots génériques
            if value.lower() in ['nom', 'prenom', 'prénom', 'famille', 'client']:
                return False

        elif field == "numero_compte":
            # Numéro de compte : format flexible
            if len(value) < 8 or len(value) > 30:
                return False
            # Doit contenir principalement des chiffres ou X
            if not re.match(r'^[0-9X\-\s]+$', value):
                return False

        elif field == "nationalite":
            # Nationalité : au moins 3 caractères
            if len(value) < 3:
                return False
            # Éviter les valeurs entièrement masquées
            if re.match(r'^X+$', value):
                return False

        elif field == "profession":
            # Profession : au moins 3 caractères
            if len(value) < 3:
                return False
            # Éviter les valeurs génériques
            if value.lower() in ['profession', 'emploi', 'travail', 'activité']:
                return False

        return True

    def extract_with_context_analysis(self, text: str, field_type: str) -> Dict[str, Any]:
        """Extraction intelligente basée sur l'analyse contextuelle"""
        logger.info(f"🧠 Analyse contextuelle pour {field_type}...")

        enhanced_info = {}

        # Analyse par lignes pour détecter les structures
        lines = text.split('\n')

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # Recherche de patterns contextuels spécifiques
            if field_type == "contract":
                # Détection intelligente du numéro de contrat
                if any(keyword in line.lower() for keyword in ['contrat', 'référence', 'dossier', 'n°']):
                    # Chercher des codes alphanumériques dans cette ligne et les suivantes
                    context_lines = lines[max(0, i-1):min(len(lines), i+3)]
                    for ctx_line in context_lines:
                        # Pattern pour codes de contrat complexes
                        contract_matches = re.findall(r'([A-Z]{2,8}[0-9\_\-]{6,15})', ctx_line)
                        if contract_matches:
                            enhanced_info['numero_contrat_contextuel'] = contract_matches[0]
                            break

                # Détection intelligente des montants
                if any(keyword in line.lower() for keyword in ['montant', 'somme', 'prêt', 'crédit', 'financement']):
                    # Chercher des montants dans le contexte
                    context_lines = lines[max(0, i-1):min(len(lines), i+3)]
                    for ctx_line in context_lines:
                        amount_matches = re.findall(r'([0-9]{3}(?:[\s,\.][0-9]{3})*(?:[\s,\.][0-9]{1,3})?)', ctx_line)
                        if amount_matches:
                            # Prendre le plus gros montant trouvé
                            amounts = [int(amt.replace(' ', '').replace(',', '').replace('.', '')) for amt in amount_matches]
                            if amounts and max(amounts) > 10000:  # Montant significatif
                                enhanced_info['montant_contextuel'] = amount_matches[amounts.index(max(amounts))]
                                break

            elif field_type == "client":
                # Détection intelligente du CIN
                if any(keyword in line.lower() for keyword in ['cin', 'identité', 'carte', 'document']):
                    context_lines = lines[max(0, i-1):min(len(lines), i+3)]
                    for ctx_line in context_lines:
                        cin_matches = re.findall(r'([0-9X]{8})', ctx_line)
                        if cin_matches:
                            enhanced_info['cin_contextuel'] = cin_matches[0]
                            break

                # Détection intelligente des téléphones
                if any(keyword in line.lower() for keyword in ['téléphone', 'mobile', 'portable', 'gsm']):
                    context_lines = lines[max(0, i-1):min(len(lines), i+3)]
                    for ctx_line in context_lines:
                        phone_matches = re.findall(r'([0-9X\s\.\-\+\(\)]{8,15})', ctx_line)
                        if phone_matches:
                            enhanced_info['telephone_contextuel'] = phone_matches[0]
                            break

        return enhanced_info

    def clean_and_validate_data(self, data: Dict[str, Any], data_type: str) -> Dict[str, Any]:
        """Nettoyage et validation finale des données extraites"""
        cleaned_data = {}

        for field, value in data.items():
            if not value or not value.strip():
                cleaned_data[field] = ""
                continue

            # Nettoyage général
            cleaned_value = value.strip()

            # Nettoyages spécifiques par type de champ
            if field in ["montant_principal", "capital_social"]:
                # Nettoyage des montants
                cleaned_value = re.sub(r'\s+', ' ', cleaned_value)
                cleaned_value = re.sub(r'(?:dinars?|DT|D|TND).*$', '', cleaned_value, flags=re.IGNORECASE).strip()

            elif field in ["taux_interet", "taux_effectif_global", "commission_gestion"]:
                # Nettoyage des taux
                cleaned_value = re.sub(r'\s+', ' ', cleaned_value)
                if not cleaned_value.endswith('%'):
                    cleaned_value += '%'

            elif field in ["telephone_mobile", "telephone_fixe", "telephone"]:
                # Nettoyage des téléphones
                cleaned_value = re.sub(r'[^\d\+\(\)\-\.\sX]', '', cleaned_value)
                cleaned_value = re.sub(r'\s+', ' ', cleaned_value).strip()

            elif field == "cin":
                # Nettoyage du CIN
                cleaned_value = re.sub(r'[^\dX]', '', cleaned_value)

            elif field in ["nom", "prenom", "nom_complet"]:
                # Nettoyage des noms
                cleaned_value = re.sub(r'\s+', ' ', cleaned_value)
                cleaned_value = cleaned_value.title()  # Première lettre en majuscule

            elif field in ["date_edition", "date_signature", "date_naissance"]:
                # Nettoyage des dates
                cleaned_value = re.sub(r'[^\dX\/\-\.]', '', cleaned_value)

            elif field == "numero_contrat":
                # Nettoyage du numéro de contrat
                cleaned_value = re.sub(r'\s+', '', cleaned_value)  # Supprimer les espaces

            # Validation finale
            if data_type == "contract" and not self.validate_contract_field(cleaned_value, field):
                cleaned_data[field] = ""
            elif data_type == "client" and not self.validate_client_field(cleaned_value, field):
                cleaned_data[field] = ""
            elif data_type == "bank" and not self.validate_bank_field(cleaned_value, field):
                cleaned_data[field] = ""
            else:
                cleaned_data[field] = cleaned_value

        return cleaned_data

    def detect_masked_fields(self, text: str) -> List[Dict]:
        """Détection avancée des champs masqués"""
        masked_fields = []
        
        masked_patterns = [
            (r'Code\s+Client\s*:?\s*(X+)', 'code_client'),
            (r'Nom.*?:?\s*(X{2,})', 'nom'),
            (r'Prénom.*?:?\s*(X{2,})', 'prenom'),
            (r'Date.*?Naissance\s*:?\s*(X+)', 'date_naissance'),
            (r'CIN\s*:?\s*(X+)', 'cin'),
            (r'Adresse.*?:?\s*(X{3,})', 'adresse'),
            (r'Mobile\s*:?\s*(X+)', 'telephone_mobile'),
            (r'Profession\s*:?\s*(X+)', 'profession'),
            (r'Nationalité\s*:?\s*(X+)', 'nationalite')
        ]
        
        for pattern, field_name in masked_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                mask_value = match.group(1)
                masked_fields.append({
                    "champ": field_name,
                    "masque": mask_value,
                    "longueur_masque": len(mask_value),
                    "position": match.start(),
                    "contexte": text[max(0, match.start()-20):match.end()+20].strip()
                })
        
        return masked_fields

    def calculate_quality_scores(self, bank_info: Dict, contract_info: Dict, client_info: Dict, masked_fields: List) -> Dict:
        """Calcul des scores de qualité d'extraction"""
        
        # Comptage des champs remplis
        bank_filled = sum(1 for v in bank_info.values() if v and v.strip())
        contract_filled = sum(1 for v in contract_info.values() if v and v.strip())
        client_filled = sum(1 for v in client_info.values() if v and v.strip())
        
        total_filled = bank_filled + contract_filled + client_filled
        total_possible = len(bank_info) + len(contract_info) + len(client_info)
        
        # Calcul des scores
        bank_score = (bank_filled / len(bank_info)) * 100 if bank_info else 0
        contract_score = (contract_filled / len(contract_info)) * 100 if contract_info else 0
        client_score = (client_filled / len(client_info)) * 100 if client_info else 0
        global_score = (total_filled / total_possible) * 100 if total_possible > 0 else 0
        
        return {
            "score_global": round(global_score, 1),
            "score_banque": round(bank_score, 1),
            "score_contrat": round(contract_score, 1),
            "score_client": round(client_score, 1),
            "champs_remplis_total": total_filled,
            "champs_possibles_total": total_possible,
            "champs_banque_remplis": bank_filled,
            "champs_contrat_remplis": contract_filled,
            "champs_client_remplis": client_filled,
            "champs_masques_detectes": len(masked_fields),
            "taux_completion": f"{total_filled}/{total_possible}"
        }

    def process_contract(self, pdf_path: str, pdf_name: str) -> Dict:
        """Traitement complet et optimisé d'un contrat"""
        logger.info(f"🚀 TRAITEMENT AVANCÉ: {pdf_name}")
        
        try:
            # 1. Extraction OCR
            logger.info("📄 Phase 1: Extraction OCR avancée...")
            ocr_text = self.extract_text_from_pdf(pdf_path, pdf_name)
            
            if not ocr_text.strip():
                raise Exception("Aucun texte extrait du PDF")
            
            logger.info(f"📝 Texte extrait: {len(ocr_text)} caractères")
            
            # 2. Extraction spécialisée par section
            logger.info("🔍 Phase 2: Extraction spécialisée...")

            bank_info = self.extract_bank_info(ocr_text)
            contract_info = self.extract_contract_info(ocr_text)
            client_info = self.extract_client_info(ocr_text)

            # 2.5. Extraction contextuelle intelligente
            logger.info("🧠 Phase 2.5: Analyse contextuelle...")
            contract_context = self.extract_with_context_analysis(ocr_text, "contract")
            client_context = self.extract_with_context_analysis(ocr_text, "client")

            # Fusion des résultats contextuels avec les résultats principaux
            for key, value in contract_context.items():
                field_name = key.replace('_contextuel', '')
                if field_name in contract_info and not contract_info[field_name]:
                    contract_info[field_name] = value
                    logger.info(f"✅ Contexte: {field_name} = {value}")

            for key, value in client_context.items():
                field_name = key.replace('_contextuel', '')
                if field_name in client_info and not client_info[field_name]:
                    client_info[field_name] = value
                    logger.info(f"✅ Contexte: {field_name} = {value}")

            # 3. Détection des champs masqués
            logger.info("🎭 Phase 3: Détection champs masqués...")
            masked_fields = self.detect_masked_fields(ocr_text)
            
            # 4. Post-traitement et nettoyage
            logger.info("🧹 Phase 4: Post-traitement et nettoyage...")
            bank_info = self.clean_and_validate_data(bank_info, "bank")
            contract_info = self.clean_and_validate_data(contract_info, "contract")
            client_info = self.clean_and_validate_data(client_info, "client")

            # 5. Calcul des scores de qualité
            logger.info("📊 Phase 5: Calcul scores qualité...")
            quality_scores = self.calculate_quality_scores(bank_info, contract_info, client_info, masked_fields)
            
            # 5. Assemblage du résultat final
            result = {
                "informations_banque": bank_info,
                "informations_contrat": contract_info,
                "informations_client": client_info,
                "champs_masques": masked_fields,
                "scores_qualite": quality_scores,
                "metadata": {
                    "fichier_source": f"{pdf_name}.pdf",
                    "extraction_timestamp": datetime.now().isoformat(),
                    "methode_extraction": "Ultra-Intelligent Contract Extractor v3.0",
                    "ocr_text_length": len(ocr_text),
                    "pages_traitees": ocr_text.count("--- Page"),
                    "version": "3.0",
                    "ameliorations": [
                        "Patterns ultra-flexibles pour numéros de contrat",
                        "Détection améliorée des CIN tunisiens",
                        "Reconnaissance contextuelle intelligente",
                        "Validation adaptative des champs",
                        "Post-traitement et nettoyage automatique",
                        "Gestion des formats masqués (X)",
                        "Détection multi-format pour montants et dates"
                    ]
                }
            }
            
            # 6. Affichage des résultats
            logger.info("✅ EXTRACTION TERMINÉE!")
            logger.info(f"📊 Score global: {quality_scores['score_global']}%")
            logger.info(f"🏛️ Banque: {quality_scores['champs_banque_remplis']} champs")
            logger.info(f"📋 Contrat: {quality_scores['champs_contrat_remplis']} champs")
            logger.info(f"👤 Client: {quality_scores['champs_client_remplis']} champs")
            logger.info(f"🎭 Masqués: {len(masked_fields)} champs")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Erreur: {str(e)}")
            return {
                "erreur": str(e),
                "informations_banque": {},
                "informations_contrat": {},
                "informations_client": {},
                "champs_masques": [],
                "scores_qualite": {"score_global": 0},
                "metadata": {
                    "fichier_source": f"{pdf_name}.pdf",
                    "extraction_timestamp": datetime.now().isoformat(),
                    "methode_extraction": "Advanced Contract Extractor v2.0 (ERROR)",
                    "erreur": str(e)
                }
            }

def main():
    """Fonction principale ultra-améliorée"""
    logger.info("🎯 === EXTRACTEUR DE CONTRATS ULTRA-INTELLIGENT V3.0 ===")
    logger.info("🚀 EXTRACTION IA CONTEXTUELLE + PATTERNS FLEXIBLES")
    logger.info("✨ Améliorations: CIN, Contrats, Montants, Dates, Contexte")
    
    # Initialisation
    extractor = AdvancedContractExtractor()
    
    # Vérifications
    if not os.path.exists(PDF_FOLDER):
        logger.error(f"❌ Dossier {PDF_FOLDER} non trouvé")
        return
    
    # Création des dossiers
    for folder in [IMG_FOLDER, TEXT_FOLDER, RESULT_FOLDER]:
        os.makedirs(folder, exist_ok=True)
    
    # Recherche des PDFs
    pdf_files = [f for f in os.listdir(PDF_FOLDER) 
                 if f.lower().endswith('.pdf') and not f.startswith('~')]
    
    if not pdf_files:
        logger.error(f"❌ Aucun PDF trouvé dans {PDF_FOLDER}")
        return
    
    logger.info(f"📁 {len(pdf_files)} fichier(s) PDF à traiter")
    
    # Traitement
    results_summary = []
    
    for i, pdf_file in enumerate(pdf_files, 1):
        logger.info(f"\n{'='*80}")
        logger.info(f"📋 FICHIER {i}/{len(pdf_files)}: {pdf_file}")
        logger.info(f"{'='*80}")
        
        pdf_path = os.path.join(PDF_FOLDER, pdf_file)
        pdf_name = pdf_file.rsplit(".", 1)[0]
        
        # Traitement
        result = extractor.process_contract(pdf_path, pdf_name)
        
        # Sauvegarde
        output_path = f"{RESULT_FOLDER}/{pdf_name}_ADVANCED.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Sauvegardé: {output_path}")
        
        # Ajout au résumé
        if "scores_qualite" in result:
            results_summary.append({
                "fichier": pdf_file,
                "score": result["scores_qualite"]["score_global"],
                "champs_total": result["scores_qualite"]["champs_remplis_total"]
            })
    
    # Résumé final
    logger.info(f"\n{'='*80}")
    logger.info("🏁 EXTRACTION TERMINÉE - RÉSUMÉ")
    logger.info(f"{'='*80}")
    
    if results_summary:
        avg_score = sum(r["score"] for r in results_summary) / len(results_summary)
        total_fields = sum(r["champs_total"] for r in results_summary)
        
        logger.info(f"📊 Score moyen: {avg_score:.1f}%")
        logger.info(f"📋 Total champs extraits: {total_fields}")
        logger.info(f"📁 Fichiers traités: {len(results_summary)}")

if __name__ == "__main__":
    main()