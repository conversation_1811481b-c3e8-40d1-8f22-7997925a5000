
from flask import Flask, render_template, request, redirect, url_for, send_from_directory
import os
import json
import uuid
from datetime import datetime
from werkzeug.utils import secure_filename

# Import your custom extractor (adjust the import path as needed)
from simple_extract_contract import AdvancedContractExtractor  # Adjust based on your file structure

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['RESULTS_FOLDER'] = 'results'
app.config['HISTORY_FILE'] = 'history.json'
app.config['ALLOWED_EXTENSIONS'] = {'pdf'}

# Create necessary folders
for folder in [app.config['UPLOAD_FOLDER'], app.config['RESULTS_FOLDER']]:
    os.makedirs(folder, exist_ok=True)

# Initialize history if it doesn't exist
if not os.path.exists(app.config['HISTORY_FILE']):
    with open(app.config['HISTORY_FILE'], 'w') as f:
        json.dump([], f)

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def read_history():
    """Read the analysis history"""
    try:
        with open(app.config['HISTORY_FILE'], 'r') as f:
            return json.load(f)
    except:
        return []

def save_history(history):
    """Save the analysis history"""
    with open(app.config['HISTORY_FILE'], 'w') as f:
        json.dump(history, f, indent=2)

def add_to_history(result_filename, original_filename, analysis_data):
    """Add an analysis to the history"""
    history = read_history()

    # Keep only the last 50 analyses
    if len(history) > 50:
        history = history[:50]

    # Extract enhanced information from V3.0 model
    bank_info = analysis_data.get('informations_banque', {})
    contract_info = analysis_data.get('informations_contrat', {})
    client_info = analysis_data.get('informations_client', {})
    quality_scores = analysis_data.get('scores_qualite', {})
    metadata = analysis_data.get('metadata', {})

    # Create a new entry with enhanced V3.0 data
    new_entry = {
        'id': str(uuid.uuid4()),
        'timestamp': datetime.now().isoformat(),
        'result_filename': result_filename,
        'original_filename': original_filename,
        'summary': {
            'client_name': f"{client_info.get('nom', '')} {client_info.get('prenom', '')}".strip() or 'Non détecté',
            'contract_type': contract_info.get('type_contrat', 'Non détecté'),
            'contract_number': contract_info.get('numero_contrat', 'Non détecté'),
            'bank_name': bank_info.get('nom_banque', 'Non détecté'),
            'amount': contract_info.get('montant_principal', 'Non détecté'),
            'duration': contract_info.get('duree', 'Non détecté'),
            'interest_rate': contract_info.get('taux_interet', 'Non détecté'),
            'cin': client_info.get('cin', 'Non détecté'),
            'masked_fields': len(analysis_data.get('champs_masques', [])),
            'global_score': quality_scores.get('score_global', 0),
            'total_fields': quality_scores.get('champs_remplis_total', 0),
            'extraction_method': metadata.get('methode_extraction', 'Standard'),
            'version': metadata.get('version', '1.0')
        }
    }
    
    # Add to the beginning of the list (most recent first)
    history.insert(0, new_entry)
    save_history(history)
    
    return new_entry['id']

@app.route('/')
def index():
    """Home page with upload form and recent history"""
    history = read_history()
    recent_history = history[:5] if len(history) > 5 else history
    return render_template('index.html', recent_history=recent_history)

@app.route('/upload', methods=['POST'])
def upload_file():
    """Process the uploaded file"""
    if 'file' not in request.files:
        return redirect(request.url)
    
    file = request.files['file']
    if file.filename == '':
        return redirect(request.url)
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Process the contract
        extractor = AdvancedContractExtractor()
        result = extractor.process_contract(filepath, filename.rsplit('.', 1)[0])
        
        # Save results
        result_filename = f"{filename.rsplit('.', 1)[0]}_results.json"
        result_path = os.path.join(app.config['RESULTS_FOLDER'], result_filename)
        with open(result_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        # Add to history
        analysis_id = add_to_history(result_filename, filename, result)
        
        # Clean up uploaded file
        os.remove(filepath)
        
        return redirect(url_for('show_results', filename=result_filename))
    
    return redirect(request.url)

@app.route('/results/<filename>')
def show_results(filename):
    """Display analysis results with V3.0 template"""
    result_path = os.path.join(app.config['RESULTS_FOLDER'], filename)

    if not os.path.exists(result_path):
        return "Résultats non trouvés", 404

    with open(result_path, 'r', encoding='utf-8') as f:
        results = json.load(f)

    # Check if it's V3.0 results (has metadata with version)
    is_v3 = results.get('metadata', {}).get('version', '1.0').startswith('3.')

    template_name = 'results_v3.html' if is_v3 else 'results.html'

    return render_template(template_name,
                           results=results,
                           filename=filename,
                           original_filename=filename.replace('_results.json', '').replace('_ADVANCED.json', '.pdf'))

@app.route('/download/<filename>', endpoint='download')
def download_file(filename):
    """Download the result file"""
    return send_from_directory(
        app.config['RESULTS_FOLDER'],
        filename,
        as_attachment=True,
        download_name=f"resultats_analyse_{filename}"
    )

@app.route('/history')
def history():
    """Full history page of analyses"""
    history_data = read_history()
    return render_template('history.html', history=history_data)

@app.route('/history/<analysis_id>')
def history_details(analysis_id):
    """Details of a specific analysis from history"""
    history_data = read_history()
    analysis = next((item for item in history_data if item['id'] == analysis_id), None)
    
    if not analysis:
        return "Analyse non trouvée", 404
    
    result_path = os.path.join(app.config['RESULTS_FOLDER'], analysis['result_filename'])
    
    if not os.path.exists(result_path):
        return "Résultats non trouvés", 404
    
    with open(result_path, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    return render_template('history_details.html', 
                          analysis=analysis,
                          results=results,
                          original_filename=analysis['original_filename'])

# Filter for formatting dates in templates
@app.template_filter('format_datetime')
def format_datetime(value):
    if value:
        try:
            dt = datetime.fromisoformat(value)
            return dt.strftime("%d/%m/%Y à %H:%M")
        except (ValueError, TypeError):
            return value
    return "Non spécifié"

if __name__ == '__main__':
    app.run(debug=True)
